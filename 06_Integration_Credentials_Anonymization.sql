-- =====================================================================================
-- COMPLETEVIEW DATABASE ANONYMIZATION - STANDALONE MODULE
-- File: 06_Integration_Credentials_Anonymization.sql

-- =====================================================================================
-- This is a standalone, pre-configured SQL module that can be executed directly
-- in SQL Server Management Studio without requiring PowerShell configuration.
-- 
-- IMPORTANT: 
-- 1. BACKUP YOUR DATABASE BEFORE RUNNING THIS SCRIPT
-- 2. Review the configuration values below before execution
-- 3. Execute modules in numerical order (01, 02, 03, etc.)
-- =====================================================================================

SET NOCOUNT ON;
SET XACT_ABORT ON;

-- =====================================================================================
-- CENTRALIZED CONFIGURATION LOADER
-- =====================================================================================

-- Verify configuration prerequisites
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationConfig')
BEGIN
    THROW 50001, 'AnonymizationConfig table not found. Please run Module 01 (Setup and Validation) first.', 1;
END

IF OBJECT_ID('dbo.fn_GetConfigValue') IS NULL
BEGIN
    THROW 50002, 'Configuration helper function fn_GetConfigValue not found. Please run Module 01 (Setup and Validation) first.', 1;
END

-- Load configuration values from centralized table
DECLARE @ExecutionID VARCHAR(50) = 'MODULE06_' + REPLACE(CAST(NEWID() AS VARCHAR(36)), '-', '');
DECLARE @ModuleName VARCHAR(100) = 'Integration Credentials Anonymization';

PRINT 'Starting ' + @ModuleName + '...';
PRINT 'Execution ID: ' + @ExecutionID;

DECLARE @AnonymizationSeed INT = CAST(dbo.fn_GetConfigValue('AnonymizationSeed') AS INT);
DECLARE @AnonymizedDomain NVARCHAR(100) = dbo.fn_GetConfigValue('AnonymizedDomain');
DECLARE @EmailSuffix NVARCHAR(100) = dbo.fn_GetConfigValue('EmailSuffix');
DECLARE @DefaultPassword NVARCHAR(100) = dbo.fn_GetConfigValue('DefaultPassword');
DECLARE @BatchSize INT = CAST(dbo.fn_GetConfigValue('BatchSize') AS INT);
DECLARE @DryRun BIT = CAST(dbo.fn_GetConfigValue('DryRun') AS BIT);
DECLARE @CommitChanges BIT = CAST(dbo.fn_GetConfigValue('CommitChanges') AS BIT);
DECLARE @UsePerformanceOptimizations BIT = CAST(dbo.fn_GetConfigValue('UsePerformanceOptimizations') AS BIT);
DECLARE @RequireRecentBackup BIT = CAST(dbo.fn_GetConfigValue('RequireRecentBackup') AS BIT);
DECLARE @BackupMaxAgeDays INT = CAST(dbo.fn_GetConfigValue('BackupMaxAgeDays') AS INT);

-- IP and path configuration
DECLARE @CVE_IPRange_Cameras NVARCHAR(20) = dbo.fn_GetConfigValue('CVE_IPRange_Cameras');
DECLARE @CVE_IPRange_Servers NVARCHAR(20) = dbo.fn_GetConfigValue('CVE_IPRange_Servers');
DECLARE @CVE_IPRange_SessionManager NVARCHAR(20) = dbo.fn_GetConfigValue('CVE_IPRange_SessionManager');
DECLARE @CVE_FilePath_Prefix NVARCHAR(100) = dbo.fn_GetConfigValue('CVE_FilePath_Prefix');
DECLARE @CVE_UNCPath_Prefix NVARCHAR(100) = dbo.fn_GetConfigValue('CVE_UNCPath_Prefix');
DECLARE @CVE_StreamProfile_Prefix NVARCHAR(50) = dbo.fn_GetConfigValue('CVE_StreamProfile_Prefix');

-- Module-specific configuration (from centralized config)
DECLARE @IntegrationPrefix NVARCHAR(50) = ISNULL(dbo.fn_GetConfigValue('IntegrationPrefix'), 'INT');
DECLARE @ExternalSystemPrefix NVARCHAR(50) = ISNULL(dbo.fn_GetConfigValue('ExternalSystemPrefix'), 'EXT');
DECLARE @WebServicePrefix NVARCHAR(50) = ISNULL(dbo.fn_GetConfigValue('WebServicePrefix'), 'WS');
DECLARE @CloudServicePrefix NVARCHAR(50) = ISNULL(dbo.fn_GetConfigValue('CloudServicePrefix'), 'CLOUD');

-- Initialize module variables
DECLARE @TotalRowsProcessed INT = 0;
DECLARE @ModuleStartTime DATETIME = GETDATE();
DECLARE @OperationStart DATETIME;
DECLARE @RowsAffected INT;

PRINT 'Configuration loaded from centralized table for Integration Credentials Anonymization';

-- =====================================================================================
-- MODULE 06: INTEGRATION CREDENTIALS ANONYMIZATION
-- =====================================================================================
-- Description: Anonymizes integration and external system credentials including
--              API keys, connection strings, certificates, and third-party service credentials
-- =====================================================================================

PRINT '=======================================================================';
PRINT 'MODULE 06: INTEGRATION CREDENTIALS ANONYMIZATION';
PRINT '=======================================================================';
PRINT 'Execution Mode: ' + CASE WHEN @DryRun = 1 THEN 'DRY RUN (Preview Only)' ELSE 'LIVE EXECUTION' END;
PRINT '';

BEGIN TRY
    BEGIN TRAN;

    -- Create pre-anonymization snapshot
    PRINT 'Creating pre-anonymization snapshot...';
    IF OBJECT_ID('dbo.sp_CreatePreAnonymizationSnapshot') IS NOT NULL
    BEGIN
        EXEC dbo.sp_CreatePreAnonymizationSnapshot 
            @ExecutionID = @ExecutionID,
            @ModuleName = @ModuleName;
    END

    -- =====================================================================================
    -- INTEGRATION POINTS ANONYMIZATION
    -- =====================================================================================
    PRINT 'Processing IntegrationPoints table...';

    -- Check if IntegrationPoints table exists
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'IntegrationPoints')
    BEGIN
        DECLARE @IntegrationRowCount INT = (SELECT COUNT(*) FROM dbo.IntegrationPoints);
        SET @OperationStart = GETDATE();
        
        IF @IntegrationRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@IntegrationRowCount AS VARCHAR) + ' integration point records to process.';
            
            -- Preview changes
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: Integration points anonymization preview:';
                SELECT TOP 10
                    IntegrationID,
                    CONCAT(@IntegrationPrefix, '-', RIGHT('000' + CAST(ROW_NUMBER() OVER (ORDER BY IntegrationID) AS VARCHAR), 3)) AS New_Name,
                    'integration-system.example.com' AS New_Hostname,
                    '[REDACTED-API-KEY]' AS New_APIKey,
                    '[REDACTED]' AS New_Username,
                    '[REDACTED]' AS New_Password,
                    '[REDACTED-CONNECTION-STRING]' AS New_ConnectionString
                FROM dbo.IntegrationPoints
                WHERE Name IS NOT NULL OR APIKey IS NOT NULL OR ConnectionString IS NOT NULL;
            END
            ELSE
            BEGIN
                -- Anonymize integration names using CTE
                WITH IntegrationNameCTE AS (
                    SELECT IntegrationID,
                           CONCAT(@IntegrationPrefix, '-', RIGHT('000' + CAST(ROW_NUMBER() OVER (ORDER BY IntegrationID) AS VARCHAR), 3)) AS NewName
                    FROM dbo.IntegrationPoints 
                    WHERE Name IS NOT NULL
                )
                UPDATE ip 
                SET Name = cte.NewName
                FROM dbo.IntegrationPoints ip
                INNER JOIN IntegrationNameCTE cte ON ip.IntegrationID = cte.IntegrationID;
                
                DECLARE @NameUpdateCount INT = @@ROWCOUNT;
                
                -- Anonymize hostnames and endpoints
                UPDATE dbo.IntegrationPoints 
                SET Hostname = 'integration-system.example.com',
                    Endpoint = '/api/v1/anonymous'
                WHERE Hostname IS NOT NULL OR Endpoint IS NOT NULL;
                
                DECLARE @HostUpdateCount INT = @@ROWCOUNT;
                
                -- Anonymize API keys
                UPDATE dbo.IntegrationPoints 
                SET APIKey = '[REDACTED-API-KEY]'
                WHERE APIKey IS NOT NULL;
                
                DECLARE @APIKeyUpdateCount INT = @@ROWCOUNT;
                
                -- Anonymize usernames and passwords
                UPDATE dbo.IntegrationPoints 
                SET Username = 'apiuser',
                    Password = '[REDACTED]'
                WHERE Username IS NOT NULL OR Password IS NOT NULL;
                
                DECLARE @CredUpdateCount INT = @@ROWCOUNT;
                
                -- Anonymize connection strings
                UPDATE dbo.IntegrationPoints 
                SET ConnectionString = '[REDACTED-CONNECTION-STRING]'
                WHERE ConnectionString IS NOT NULL;
                
                DECLARE @ConnStringUpdateCount INT = @@ROWCOUNT;
                
                -- Anonymize certificates and tokens
                UPDATE dbo.IntegrationPoints 
                SET Certificate = '[REDACTED-CERTIFICATE]',
                    AccessToken = '[REDACTED-TOKEN]',
                    RefreshToken = '[REDACTED-REFRESH-TOKEN]'
                WHERE Certificate IS NOT NULL OR AccessToken IS NOT NULL OR RefreshToken IS NOT NULL;
                
                DECLARE @CertTokenUpdateCount INT = @@ROWCOUNT;
                
                SET @RowsAffected = @NameUpdateCount + @HostUpdateCount + @APIKeyUpdateCount + @CredUpdateCount + @ConnStringUpdateCount + @CertTokenUpdateCount;
                
                -- Buffer operations for aggregated logging
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES 
                    ('Integration Data', 'IntegrationPoints', 'Name', @NameUpdateCount, 'Sequential Naming', @OperationStart, GETDATE(),
                     CONCAT('Integration names anonymized with ', @IntegrationPrefix, '-### pattern')),
                    ('Integration Data', 'IntegrationPoints', 'Hostname', @HostUpdateCount, 'Generic Replacement', @OperationStart, GETDATE(),
                     'Integration hostnames and endpoints anonymized'),
                    ('Integration Data', 'IntegrationPoints', 'APIKey', @APIKeyUpdateCount, 'Credential Redaction', @OperationStart, GETDATE(),
                     'API keys redacted'),
                    ('Integration Data', 'IntegrationPoints', 'Credentials', @CredUpdateCount, 'Credential Redaction', @OperationStart, GETDATE(),
                     'Integration credentials anonymized'),
                    ('Integration Data', 'IntegrationPoints', 'ConnectionString', @ConnStringUpdateCount, 'Credential Redaction', @OperationStart, GETDATE(),
                     'Connection strings redacted'),
                    ('Integration Data', 'IntegrationPoints', 'Certificates', @CertTokenUpdateCount, 'Credential Redaction', @OperationStart, GETDATE(),
                     'Certificates and tokens redacted');
                
                PRINT 'Updated ' + CAST(@NameUpdateCount AS VARCHAR) + ' integration names';
                PRINT 'Updated ' + CAST(@HostUpdateCount AS VARCHAR) + ' hostnames/endpoints';
                PRINT 'Updated ' + CAST(@APIKeyUpdateCount AS VARCHAR) + ' API keys';
                PRINT 'Updated ' + CAST(@CredUpdateCount AS VARCHAR) + ' credentials';
                PRINT 'Updated ' + CAST(@ConnStringUpdateCount AS VARCHAR) + ' connection strings';
                PRINT 'Updated ' + CAST(@CertTokenUpdateCount AS VARCHAR) + ' certificates/tokens';
            END
            
            SET @TotalRowsProcessed = @TotalRowsProcessed + @RowsAffected;
        END
        ELSE
        BEGIN
            PRINT 'No integration point records found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'IntegrationPoints table not found in database.';
    END

    -- =====================================================================================
    -- EXTERNAL SYSTEMS ANONYMIZATION
    -- =====================================================================================
    PRINT '';
    PRINT 'Processing ExternalSystems table...';

    -- Check if ExternalSystems table exists
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'ExternalSystems')
    BEGIN
        DECLARE @ExternalRowCount INT = (SELECT COUNT(*) FROM dbo.ExternalSystems);
        SET @OperationStart = GETDATE();
        
        IF @ExternalRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@ExternalRowCount AS VARCHAR) + ' external system records to process.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: External systems anonymization preview:';
                SELECT TOP 10
                    SystemID,
                    CONCAT(@ExternalSystemPrefix, '-', RIGHT('000' + CAST(ROW_NUMBER() OVER (ORDER BY SystemID) AS VARCHAR), 3)) AS New_SystemName,
                    'external.example.com' AS New_ServerAddress,
                    '[REDACTED]' AS New_SharedKey,
                    '[REDACTED-CERTIFICATE]' AS New_SSLCertificate
                FROM dbo.ExternalSystems;
            END
            ELSE
            BEGIN
                -- Anonymize external system data using CTE
                WITH ExternalSystemCTE AS (
                    SELECT SystemID,
                           CONCAT(@ExternalSystemPrefix, '-', RIGHT('000' + CAST(ROW_NUMBER() OVER (ORDER BY SystemID) AS VARCHAR), 3)) AS NewSystemName
                    FROM dbo.ExternalSystems
                )
                UPDATE es 
                SET SystemName = cte.NewSystemName,
                    ServerAddress = 'external.example.com',
                    SharedKey = '[REDACTED]',
                    SSLCertificate = '[REDACTED-CERTIFICATE]',
                    Username = 'external_user',
                    Password = '[REDACTED]'
                FROM dbo.ExternalSystems es
                INNER JOIN ExternalSystemCTE cte ON es.SystemID = cte.SystemID;
                
                DECLARE @ExternalUpdateCount INT = @@ROWCOUNT;
                
                -- Buffer operation for aggregated logging
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('Integration Data', 'ExternalSystems', 'SystemData', @ExternalUpdateCount, 'Sequential Naming', @OperationStart, GETDATE(),
                        CONCAT('External system configurations anonymized with ', @ExternalSystemPrefix, '-### pattern'));
                
                PRINT 'Updated ' + CAST(@ExternalUpdateCount AS VARCHAR) + ' external system records';
                SET @TotalRowsProcessed = @TotalRowsProcessed + @ExternalUpdateCount;
            END
        END
        ELSE
        BEGIN
            PRINT 'No external system records found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'ExternalSystems table not found - skipping external systems anonymization';
    END

    -- =====================================================================================
    -- WEB SERVICES ANONYMIZATION
    -- =====================================================================================
    PRINT '';
    PRINT 'Processing WebServices table...';

    -- Check if WebServices table exists
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'WebServices')
    BEGIN
        DECLARE @WebServiceRowCount INT = (SELECT COUNT(*) FROM dbo.WebServices);
        SET @OperationStart = GETDATE();
        
        IF @WebServiceRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@WebServiceRowCount AS VARCHAR) + ' web service records to process.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: Web services anonymization preview:';
                SELECT TOP 10
                    ServiceID,
                    CONCAT(@WebServicePrefix, '-', RIGHT('000' + CAST(ROW_NUMBER() OVER (ORDER BY ServiceID) AS VARCHAR), 3)) AS New_ServiceName,
                    'https://api.example.com/service' AS New_ServiceURL,
                    '[REDACTED-API-KEY]' AS New_APIKey,
                    '[REDACTED-SECRET]' AS New_ClientSecret
                FROM dbo.WebServices;
            END
            ELSE
            BEGIN
                -- Anonymize web service data using CTE
                WITH WebServiceCTE AS (
                    SELECT ServiceID,
                           CONCAT(@WebServicePrefix, '-', RIGHT('000' + CAST(ROW_NUMBER() OVER (ORDER BY ServiceID) AS VARCHAR), 3)) AS NewServiceName
                    FROM dbo.WebServices
                )
                UPDATE ws 
                SET ServiceName = cte.NewServiceName,
                    ServiceURL = 'https://api.example.com/service',
                    APIKey = '[REDACTED-API-KEY]',
                    ClientSecret = '[REDACTED-SECRET]',
                    Username = 'webservice_user',
                    Password = '[REDACTED]'
                FROM dbo.WebServices ws
                INNER JOIN WebServiceCTE cte ON ws.ServiceID = cte.ServiceID;
                
                DECLARE @WebServiceUpdateCount INT = @@ROWCOUNT;
                
                -- Buffer operation for aggregated logging
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('Integration Data', 'WebServices', 'ServiceData', @WebServiceUpdateCount, 'Sequential Naming', @OperationStart, GETDATE(),
                        CONCAT('Web service configurations anonymized with ', @WebServicePrefix, '-### pattern'));
                
                PRINT 'Updated ' + CAST(@WebServiceUpdateCount AS VARCHAR) + ' web service records';
                SET @TotalRowsProcessed = @TotalRowsProcessed + @WebServiceUpdateCount;
            END
        END
        ELSE
        BEGIN
            PRINT 'No web service records found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'WebServices table not found - skipping web services anonymization';
    END

    -- =====================================================================================
    -- SMTP SETTINGS ANONYMIZATION
    -- =====================================================================================
    PRINT '';
    PRINT 'Processing SMTP settings...';

    -- Check if SMTPSettings table exists
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'SMTPSettings')
    BEGIN
        DECLARE @SMTPRowCount INT = (SELECT COUNT(*) FROM dbo.SMTPSettings);
        SET @OperationStart = GETDATE();
        
        IF @SMTPRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@SMTPRowCount AS VARCHAR) + ' SMTP configuration records to process.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: SMTP settings anonymization preview:';
                SELECT TOP 10
                    SettingID,
                    'smtp.example.com' AS New_SMTPServer,
                    '<EMAIL>' AS New_FromAddress,
                    '[REDACTED]@example.com' AS New_Username,
                    '[REDACTED-PASSWORD]' AS New_Password
                FROM dbo.SMTPSettings;
            END
            ELSE
            BEGIN
                -- Anonymize SMTP settings
                UPDATE dbo.SMTPSettings 
                SET SMTPServer = 'smtp.example.com',
                    FromAddress = '<EMAIL>',
                    Username = '[REDACTED]@example.com',
                    Password = '[REDACTED-PASSWORD]',
                    Port = 587,
                    EnableSSL = 1;
                
                DECLARE @SMTPUpdateCount INT = @@ROWCOUNT;
                
                -- Buffer operation for aggregated logging
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('Integration Data', 'SMTPSettings', 'SMTPConfiguration', @SMTPUpdateCount, 'Credential Redaction', @OperationStart, GETDATE(),
                        'SMTP settings and credentials anonymized with standard secure values');
                
                PRINT 'Updated ' + CAST(@SMTPUpdateCount AS VARCHAR) + ' SMTP configuration records';
                SET @TotalRowsProcessed = @TotalRowsProcessed + @SMTPUpdateCount;
            END
        END
        ELSE
        BEGIN
            PRINT 'No SMTP configuration records found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'SMTPSettings table not found - skipping SMTP settings anonymization';
    END

    -- =====================================================================================
    -- CLOUD SERVICES ANONYMIZATION
    -- =====================================================================================
    PRINT '';
    PRINT 'Processing CloudServices table...';

    -- Check if CloudServices table exists
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'CloudServices')
    BEGIN
        DECLARE @CloudRowCount INT = (SELECT COUNT(*) FROM dbo.CloudServices);
        SET @OperationStart = GETDATE();
        
        IF @CloudRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@CloudRowCount AS VARCHAR) + ' cloud service records to process.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: Cloud services anonymization preview:';
                SELECT TOP 10
                    ServiceID,
                    CONCAT(@CloudServicePrefix, '-', RIGHT('000' + CAST(ROW_NUMBER() OVER (ORDER BY ServiceID) AS VARCHAR), 3)) AS New_ServiceName,
                    '[REDACTED-ACCESS-KEY]' AS New_AccessKey,
                    '[REDACTED-SECRET-KEY]' AS New_SecretKey,
                    'https://cloud.example.com' AS New_ServiceEndpoint
                FROM dbo.CloudServices;
            END
            ELSE
            BEGIN
                -- Anonymize cloud service data using CTE
                WITH CloudServiceCTE AS (
                    SELECT ServiceID,
                           CONCAT(@CloudServicePrefix, '-', RIGHT('000' + CAST(ROW_NUMBER() OVER (ORDER BY ServiceID) AS VARCHAR), 3)) AS NewServiceName
                    FROM dbo.CloudServices
                )
                UPDATE cs 
                SET ServiceName = cte.NewServiceName,
                    AccessKey = '[REDACTED-ACCESS-KEY]',
                    SecretKey = '[REDACTED-SECRET-KEY]',
                    ServiceEndpoint = 'https://cloud.example.com',
                    Region = 'us-east-1',
                    BucketName = 'anonymized-bucket'
                FROM dbo.CloudServices cs
                INNER JOIN CloudServiceCTE cte ON cs.ServiceID = cte.ServiceID;
                
                DECLARE @CloudUpdateCount INT = @@ROWCOUNT;
                
                -- Buffer operation for aggregated logging
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('Integration Data', 'CloudServices', 'CloudConfiguration', @CloudUpdateCount, 'Sequential Naming', @OperationStart, GETDATE(),
                        CONCAT('Cloud service configurations anonymized with ', @CloudServicePrefix, '-### pattern'));
                
                PRINT 'Updated ' + CAST(@CloudUpdateCount AS VARCHAR) + ' cloud service records';
                SET @TotalRowsProcessed = @TotalRowsProcessed + @CloudUpdateCount;
            END
        END
        ELSE
        BEGIN
            PRINT 'No cloud service records found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'CloudServices table not found - skipping cloud services anonymization';
    END

    -- =====================================================================================
    -- DATABASE CONNECTION STRINGS
    -- =====================================================================================
    PRINT '';
    PRINT 'Processing database connection strings...';

    -- Anonymize database connection configurations
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'DatabaseConnections')
    BEGIN
        DECLARE @DBConnectionRowCount INT = (SELECT COUNT(*) FROM dbo.DatabaseConnections);
        SET @OperationStart = GETDATE();
        
        IF @DBConnectionRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@DBConnectionRowCount AS VARCHAR) + ' database connection records to process.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: Database connections anonymization preview:';
                SELECT TOP 10
                    ConnectionID,
                    'db.example.com' AS New_ServerName,
                    '[REDACTED-CONNECTION-STRING]' AS New_ConnectionString,
                    'dbuser' AS New_Username,
                    '[REDACTED]' AS New_Password
                FROM dbo.DatabaseConnections;
            END
            ELSE
            BEGIN
                -- Anonymize database connection strings
                UPDATE dbo.DatabaseConnections 
                SET ServerName = 'db.example.com',
                    ConnectionString = '[REDACTED-CONNECTION-STRING]',
                    Username = 'dbuser',
                    Password = '[REDACTED]',
                    DatabaseName = 'anonymized_db'
                WHERE ServerName IS NOT NULL OR ConnectionString IS NOT NULL;
                
                DECLARE @DBConnectionUpdateCount INT = @@ROWCOUNT;
                
                -- Buffer operation for aggregated logging
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('Integration Data', 'DatabaseConnections', 'ConnectionData', @DBConnectionUpdateCount, 'Credential Redaction', @OperationStart, GETDATE(),
                        'Database connection strings and credentials anonymized');
                
                PRINT 'Updated ' + CAST(@DBConnectionUpdateCount AS VARCHAR) + ' database connection records';
                SET @TotalRowsProcessed = @TotalRowsProcessed + @DBConnectionUpdateCount;
            END
        END
        ELSE
        BEGIN
            PRINT 'No database connection records found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'DatabaseConnections table not found - skipping database connections anonymization';
    END

    -- =====================================================================================
    -- LDAP/ACTIVE DIRECTORY INTEGRATION
    -- =====================================================================================
    PRINT '';
    PRINT 'Processing LDAP/Active Directory integration settings...';

    -- Anonymize LDAP/AD configurations
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'LDAPSettings')
    BEGIN
        DECLARE @LDAPRowCount INT = (SELECT COUNT(*) FROM dbo.LDAPSettings);
        SET @OperationStart = GETDATE();
        
        IF @LDAPRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@LDAPRowCount AS VARCHAR) + ' LDAP configuration records to process.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: LDAP settings anonymization preview:';
                SELECT TOP 10
                    SettingID,
                    'ldap.example.com' AS New_LDAPServer,
                    'DC=example,DC=com' AS New_BaseDN,
                    'ldapuser' AS New_BindUser,
                    '[REDACTED-PASSWORD]' AS New_BindPassword
                FROM dbo.LDAPSettings;
            END
            ELSE
            BEGIN
                -- Anonymize LDAP settings
                UPDATE dbo.LDAPSettings 
                SET LDAPServer = 'ldap.example.com',
                    BaseDN = 'DC=example,DC=com',
                    BindUser = 'ldapuser',
                    BindPassword = '[REDACTED-PASSWORD]',
                    Port = 389,
                    UseSSL = 1
                WHERE LDAPServer IS NOT NULL OR BindUser IS NOT NULL;
                
                DECLARE @LDAPUpdateCount INT = @@ROWCOUNT;
                
                -- Buffer operation for aggregated logging
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('Integration Data', 'LDAPSettings', 'LDAPConfiguration', @LDAPUpdateCount, 'Credential Redaction', @OperationStart, GETDATE(),
                        'LDAP/AD settings and credentials anonymized with standard secure values');
                
                PRINT 'Updated ' + CAST(@LDAPUpdateCount AS VARCHAR) + ' LDAP configuration records';
                SET @TotalRowsProcessed = @TotalRowsProcessed + @LDAPUpdateCount;
            END
        END
        ELSE
        BEGIN
            PRINT 'No LDAP configuration records found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'LDAPSettings table not found - skipping LDAP settings anonymization';
    END

    -- =====================================================================================
    -- API CONFIGURATION TABLES
    -- =====================================================================================
    PRINT '';
    PRINT 'Processing API configuration tables...';

    -- Anonymize APIConfiguration table
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'APIConfiguration')
    BEGIN
        DECLARE @APIConfigRowCount INT = (SELECT COUNT(*) FROM dbo.APIConfiguration);
        SET @OperationStart = GETDATE();
        
        IF @APIConfigRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@APIConfigRowCount AS VARCHAR) + ' API configuration records to process.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: API configuration anonymization preview:';
                SELECT TOP 10
                    ConfigID,
                    '[REDACTED-API-KEY]' AS New_APIKey,
                    '[REDACTED-SECRET]' AS New_APISecret,
                    'https://api.example.com' AS New_BaseURL
                FROM dbo.APIConfiguration;
            END
            ELSE
            BEGIN
                -- Anonymize API configuration
                UPDATE dbo.APIConfiguration 
                SET APIKey = '[REDACTED-API-KEY]',
                    APISecret = '[REDACTED-SECRET]',
                    BaseURL = 'https://api.example.com',
                    CallbackURL = 'https://callback.example.com'
                WHERE APIKey IS NOT NULL OR APISecret IS NOT NULL;
                
                DECLARE @APIConfigUpdateCount INT = @@ROWCOUNT;
                
                -- Buffer operation for aggregated logging
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('Integration Data', 'APIConfiguration', 'APICredentials', @APIConfigUpdateCount, 'Credential Redaction', @OperationStart, GETDATE(),
                        'API configuration keys and secrets anonymized');
                
                PRINT 'Updated ' + CAST(@APIConfigUpdateCount AS VARCHAR) + ' API configuration records';
                SET @TotalRowsProcessed = @TotalRowsProcessed + @APIConfigUpdateCount;
            END
        END
        ELSE
        BEGIN
            PRINT 'No API configuration records found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'APIConfiguration table not found - skipping API configuration anonymization';
    END

    -- =====================================================================================
    -- MODULE COMPLETION
    -- =====================================================================================
    DECLARE @ModuleEndTime DATETIME = GETDATE();
    DECLARE @ModuleExecutionTime INT = DATEDIFF(MILLISECOND, @ModuleStartTime, @ModuleEndTime);

    -- Flush aggregated logging buffer to reduce transaction log bloat
    IF OBJECT_ID('dbo.sp_FlushAnonymizationBuffer') IS NOT NULL
    BEGIN
        EXEC dbo.sp_FlushAnonymizationBuffer @DryRun = @DryRun;
    END

    -- Log module completion
    INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, ColumnName, RowsAffected, AnonymizationType, ExecutionTimeMs, BatchProcessed, Notes)
    VALUES ('Integration Credentials', 'MODULE_COMPLETE', 'ALL', @TotalRowsProcessed, 'Module Execution', 
            @ModuleExecutionTime, 0, 
            'Integration credentials anonymization completed in ' + CAST(DATEDIFF(SECOND, @ModuleStartTime, @ModuleEndTime) AS VARCHAR) + ' seconds');

    PRINT '';
    PRINT '=======================================================================';
    PRINT 'INTEGRATION CREDENTIALS ANONYMIZATION MODULE COMPLETED';
    PRINT '=======================================================================';
    PRINT 'Total execution time: ' + CAST(DATEDIFF(SECOND, @ModuleStartTime, @ModuleEndTime) AS VARCHAR) + ' seconds';
    PRINT 'Total rows processed: ' + CAST(@TotalRowsProcessed AS VARCHAR);
    PRINT 'Operations completed: Integration points, external systems, web services, SMTP, cloud services, database connections, LDAP, API configurations';
    PRINT 'Dry run mode: ' + CASE WHEN @DryRun = 1 THEN 'YES (No data modified)' ELSE 'NO (Data modified)' END;
    PRINT '=======================================================================';

    -- Create post-anonymization snapshot
    IF @DryRun = 0 AND OBJECT_ID('dbo.sp_CreatePostAnonymizationSnapshot') IS NOT NULL
    BEGIN
        PRINT 'Creating post-anonymization snapshot...';
        EXEC dbo.sp_CreatePostAnonymizationSnapshot 
            @ExecutionID = @ExecutionID,
            @ModuleName = @ModuleName;
    END

    IF @DryRun = 1 OR @CommitChanges = 0 
        ROLLBACK TRAN 
    ELSE 
        COMMIT TRAN;

END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0 ROLLBACK TRAN;
    
    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();
    
    PRINT 'ERROR in Integration Credentials Anonymization: ' + @ErrorMessage;
    
    -- Log the error if possible
    IF OBJECT_ID('dbo.AnonymizationLog') IS NOT NULL
    BEGIN
        INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, ColumnName, RowsAffected, AnonymizationType, Notes)
        VALUES ('Integration Credentials', 'ERROR', 'MODULE_FAILURE', 0, 'Error', @ErrorMessage);
    END
    
    RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
END CATCH