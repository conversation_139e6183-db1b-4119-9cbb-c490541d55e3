-- =====================================================================================
-- CONFIG_MANAGEMENT.sql - Unified Configuration Management System
-- =====================================================================================
-- This utility consolidates all configuration management functionality:
-- - Configuration table redesign (performance-optimized strongly-typed structure)
-- - Modern typed configuration accessor functions
-- - Configuration validation, loading, export/import, and management operations
-- - Auto-detection and migration between legacy and modern table structures
-- - Centralized constants and magic values from all modules
--
-- CONSOLIDATES THESE FILES:
-- - CONFIG_TABLE_REDESIGN.sql - Performance-optimized table structure
-- - TYPED_CONFIG_FUNCTIONS.sql - Modern typed accessor functions  
-- - CONFIG_MANAGEMENT.sql - Configuration management operations
--
-- FEATURES:
-- - Auto-detects legacy (VARCHAR(MAX)) vs modern (strongly-typed) table structures
-- - Provides seamless migration from legacy to modern structure
-- - Configuration validation, loading, export/import, and bulk operations
-- - Centralized magic constants and patterns from all anonymization modules
-- - Transaction safety and rollback capabilities
-- - Both legacy compatibility and modern performance optimization
-- =====================================================================================

SET NOCOUNT ON;
SET XACT_ABORT ON;

PRINT '=====================================================================================';
PRINT 'CONFIG MANAGEMENT SYSTEM - Unified Configuration Management';
PRINT '=====================================================================================';
PRINT 'Consolidates: CONFIG_TABLE_REDESIGN + TYPED_CONFIG_FUNCTIONS + CONFIG_MANAGEMENT';
PRINT '';

-- =====================================================================================
-- STEP 1: TABLE STRUCTURE DETECTION AND ANALYSIS
-- =====================================================================================

-- Check if configuration table exists
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationConfig')
BEGIN
    PRINT 'WARNING: AnonymizationConfig table not found.';
    PRINT 'This utility can create the table, but Module 01 should normally be run first.';
    PRINT 'Continuing with table creation...';
    PRINT '';
END

-- Detect current table structure
DECLARE @HasModernStructure BIT = 0;
DECLARE @HasLegacyStructure BIT = 0;
DECLARE @TableExists BIT = 0;

IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationConfig')
BEGIN
    SET @TableExists = 1;
    
    IF COL_LENGTH('dbo.AnonymizationConfig', 'StringValue') IS NOT NULL
        SET @HasModernStructure = 1;

    IF COL_LENGTH('dbo.AnonymizationConfig', 'ConfigValue') IS NOT NULL
        SET @HasLegacyStructure = 1;
END

PRINT 'Configuration table analysis:';
PRINT '  Table exists: ' + CASE WHEN @TableExists = 1 THEN 'YES' ELSE 'NO' END;
PRINT '  Legacy structure (ConfigValue VARCHAR(MAX)): ' + CASE WHEN @HasLegacyStructure = 1 THEN 'PRESENT' ELSE 'NOT FOUND' END;
PRINT '  Modern structure (StringValue/IntValue/etc): ' + CASE WHEN @HasModernStructure = 1 THEN 'PRESENT' ELSE 'NOT FOUND' END;
PRINT '';

-- =====================================================================================
-- STEP 2: TABLE MIGRATION TO MODERN STRUCTURE (if needed)
-- =====================================================================================

-- Prompt for migration decision
IF @HasLegacyStructure = 1 AND @HasModernStructure = 0
BEGIN
    PRINT 'MIGRATION OPPORTUNITY DETECTED:';
    PRINT '===============================';
    PRINT 'Your configuration table uses the legacy VARCHAR(MAX) structure.';
    PRINT 'The modern strongly-typed structure offers significant performance benefits:';
    PRINT '- Eliminates string-based type conversion overhead';
    PRINT '- Better row density and scan performance';
    PRINT '- True type safety with proper constraints';
    PRINT '- Optimized query plans and indexing';
    PRINT '';
    PRINT 'MIGRATION PROCESS:';
    PRINT '1. Backup existing configuration';
    PRINT '2. Create new strongly-typed table structure';
    PRINT '3. Migrate existing configuration values';
    PRINT '4. Create modern typed accessor functions';
    PRINT '';
    
    -- Backup existing configuration
    PRINT 'Creating backup of existing configuration...';
    
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationConfig_Backup')
        DROP TABLE dbo.AnonymizationConfig_Backup;
    
    SELECT * INTO dbo.AnonymizationConfig_Backup FROM dbo.AnonymizationConfig;
    PRINT 'BACKUP CREATED: AnonymizationConfig_Backup';
    
    -- Store existing values for migration
    IF OBJECT_ID('tempdb..#LegacyConfig') IS NOT NULL DROP TABLE #LegacyConfig;
    SELECT ConfigKey, ConfigValue, DataType, Description 
    INTO #LegacyConfig 
    FROM dbo.AnonymizationConfig;
    
    -- Drop and recreate with modern structure
    DROP TABLE dbo.AnonymizationConfig;
    PRINT 'LEGACY TABLE STRUCTURE REMOVED';
    
    SET @HasLegacyStructure = 0;
    SET @HasModernStructure = 1; -- Will be set after creation
END

-- =====================================================================================
-- STEP 3: CREATE MODERN TABLE STRUCTURE (if needed)
-- =====================================================================================

IF @TableExists = 0 OR (@HasLegacyStructure = 0 AND @HasModernStructure = 0)
BEGIN
    PRINT 'Creating modern strongly-typed configuration table...';
    
    CREATE TABLE dbo.AnonymizationConfig (
        ConfigKey VARCHAR(100) NOT NULL PRIMARY KEY,
        
        -- Strongly-typed value columns (use appropriate type, others NULL)
        StringValue NVARCHAR(256) NULL,        -- Fixed width for better density
        IntValue INT NULL,
        BitValue BIT NULL,
        DecimalValue DECIMAL(18,6) NULL,
        DateValue DATETIME2 NULL,
        
        -- JSON for complex configurations with validation
        JsonValue NVARCHAR(4000) NULL 
            CONSTRAINT CK_AnonymizationConfig_ValidJson 
            CHECK (JsonValue IS NULL OR ISJSON(JsonValue) = 1),
        
        -- Metadata columns
        DataType VARCHAR(20) NOT NULL 
            CONSTRAINT CK_AnonymizationConfig_DataType 
            CHECK (DataType IN ('STRING', 'INT', 'BIT', 'DECIMAL', 'DATE', 'JSON')),
        Description VARCHAR(500) NULL,
        CreatedDate DATETIME2 DEFAULT GETDATE(),
        ModifiedDate DATETIME2 DEFAULT GETDATE(),
        
        -- Ensure only one value column is populated per row
        CONSTRAINT CK_AnonymizationConfig_SingleValue 
        CHECK (
            (CASE WHEN StringValue IS NOT NULL THEN 1 ELSE 0 END +
             CASE WHEN IntValue IS NOT NULL THEN 1 ELSE 0 END +
             CASE WHEN BitValue IS NOT NULL THEN 1 ELSE 0 END +
             CASE WHEN DecimalValue IS NOT NULL THEN 1 ELSE 0 END +
             CASE WHEN DateValue IS NOT NULL THEN 1 ELSE 0 END +
             CASE WHEN JsonValue IS NOT NULL THEN 1 ELSE 0 END) = 1
        )
    );
    
    -- Create index for performance
    CREATE NONCLUSTERED INDEX IX_AnonymizationConfig_DataType 
    ON dbo.AnonymizationConfig (DataType) 
    INCLUDE (StringValue, IntValue, BitValue, DecimalValue, DateValue, JsonValue);
    
    PRINT 'MODERN TABLE STRUCTURE CREATED';
    SET @HasModernStructure = 1;
END

-- =====================================================================================
-- STEP 4: MIGRATE LEGACY DATA (if needed)
-- =====================================================================================

IF OBJECT_ID('tempdb..#LegacyConfig') IS NOT NULL
BEGIN
    PRINT 'Migrating legacy configuration data to modern structure...';
    
    -- Migrate each configuration value with proper typing
    DECLARE @ConfigKey VARCHAR(100), @ConfigValue VARCHAR(MAX), @DataType VARCHAR(20), @Description VARCHAR(500);
    DECLARE migration_cursor CURSOR FOR
    SELECT ConfigKey, ConfigValue, DataType, Description FROM #LegacyConfig;
    
    OPEN migration_cursor;
    FETCH NEXT FROM migration_cursor INTO @ConfigKey, @ConfigValue, @DataType, @Description;
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        -- Insert with proper typing based on DataType
        IF @DataType = 'STRING'
            INSERT INTO dbo.AnonymizationConfig (ConfigKey, DataType, StringValue, Description)
            VALUES (@ConfigKey, @DataType, LEFT(@ConfigValue, 256), @Description);
        ELSE IF @DataType = 'INT'
            INSERT INTO dbo.AnonymizationConfig (ConfigKey, DataType, IntValue, Description)
            VALUES (@ConfigKey, @DataType, TRY_CAST(@ConfigValue AS INT), @Description);
        ELSE IF @DataType = 'BIT'
            INSERT INTO dbo.AnonymizationConfig (ConfigKey, DataType, BitValue, Description)
            VALUES (@ConfigKey, @DataType, TRY_CAST(@ConfigValue AS BIT), @Description);
        ELSE IF @DataType = 'DECIMAL'
            INSERT INTO dbo.AnonymizationConfig (ConfigKey, DataType, DecimalValue, Description)
            VALUES (@ConfigKey, @DataType, TRY_CAST(@ConfigValue AS DECIMAL(18,6)), @Description);
        ELSE IF @DataType = 'DATE'
            INSERT INTO dbo.AnonymizationConfig (ConfigKey, DataType, DateValue, Description)
            VALUES (@ConfigKey, @DataType, TRY_CAST(@ConfigValue AS DATETIME2), @Description);
        ELSE IF @DataType = 'JSON'
            INSERT INTO dbo.AnonymizationConfig (ConfigKey, DataType, JsonValue, Description)
            VALUES (@ConfigKey, @DataType, @ConfigValue, @Description);
        
        FETCH NEXT FROM migration_cursor INTO @ConfigKey, @ConfigValue, @DataType, @Description;
    END
    
    CLOSE migration_cursor;
    DEALLOCATE migration_cursor;
    
    DROP TABLE #LegacyConfig;
    PRINT 'LEGACY DATA MIGRATION COMPLETED';
END

-- =====================================================================================
-- STEP 5: LOAD DEFAULT CONFIGURATION (if table is empty)
-- =====================================================================================

DECLARE @ConfigCount INT = (SELECT COUNT(*) FROM dbo.AnonymizationConfig);

IF @ConfigCount = 0
BEGIN
    PRINT 'Loading default configuration...';
    
    -- Core anonymization settings
    INSERT INTO dbo.AnonymizationConfig (ConfigKey, DataType, IntValue, Description) VALUES
        ('AnonymizationSeed', 'INT', 12345, 'Seed for deterministic anonymization'),
        ('BatchSize', 'INT', 1000, 'Processing batch size for large operations'),
        ('BackupMaxAgeDays', 'INT', 7, 'Maximum age in days for backup validation'),
        ('FragmentationThreshold', 'INT', 30, 'Index fragmentation threshold for rebuild'),
        ('StatisticsAgeThreshold', 'INT', 7, 'Days after which statistics are considered stale'),
        ('MaxExecutionTimeMinutes', 'INT', 120, 'Maximum execution time per module in minutes'),
        ('VerificationSampleSize', 'INT', 100, 'Number of records to sample for verification');

    INSERT INTO dbo.AnonymizationConfig (ConfigKey, DataType, StringValue, Description) VALUES
        ('AnonymizedDomain', 'STRING', 'example.com', 'Domain for anonymized email addresses'),
        ('EmailSuffix', 'STRING', '@company.local', 'Email suffix for anonymized addresses'),
        ('DefaultPassword', 'STRING', '[REDACTED]', 'Default password for anonymized accounts'),
        ('CVE_IPRange_Cameras', 'STRING', '********/16', 'IP range for anonymized cameras'),
        ('CVE_IPRange_Servers', 'STRING', '********/16', 'IP range for anonymized servers'),
        ('CVE_IPRange_SessionManager', 'STRING', '********/16', 'IP range for session managers'),
        ('CVE_IPRange_Alarms', 'STRING', '********/16', 'IP range for alarm devices'),
        ('CVE_IPRange_Audio', 'STRING', '********/16', 'IP range for audio devices'),
        ('CVE_FilePath_Prefix', 'STRING', 'C:\AnonymizedData', 'File path prefix for anonymized paths'),
        ('CVE_UNCPath_Prefix', 'STRING', '\\anonymized-server\share', 'UNC path prefix for network shares'),
        ('CVE_StreamProfile_Prefix', 'STRING', 'Profile', 'Stream profile prefix for anonymized data');

    INSERT INTO dbo.AnonymizationConfig (ConfigKey, DataType, BitValue, Description) VALUES
        ('DryRun', 'BIT', 0, 'Set to 1 for preview mode, 0 for actual execution'),
        ('CommitChanges', 'BIT', 1, 'Set to 1 to commit changes, 0 to rollback'),
        ('UsePerformanceOptimizations', 'BIT', 1, 'Enable performance optimizations'),
        ('RequireRecentBackup', 'BIT', 0, 'Require recent backup validation before execution'),
        ('PatternDetectionEnabled', 'BIT', 1, 'Enable pattern-based verification'),
        ('HashVerificationEnabled', 'BIT', 1, 'Enable hash-based verification');

    -- Centralized magic constants and patterns (JSON configuration)
    INSERT INTO dbo.AnonymizationConfig (ConfigKey, DataType, JsonValue, Description) VALUES
        ('AnonymizationPatterns', 'JSON',
         '{"CameraPrefix": "Camera_", "UserPrefix": "User", "ServerPrefix": "Server", "ClientHostname": "anonymized-client.example.com", "SMTPServer": "smtp.example.com", "IntegrationHostname": "integration-system.example.com", "ExternalServer": "external.example.com", "CloudEndpoint": "https://cloud.example.com", "DomainSuffix": ".example.com", "EmailDomain": "@company.local"}',
         'Centralized anonymization patterns and constants'),
        ('IPRanges', 'JSON',
         '{"CameraRange": "********/16", "ServerRange": "********/16", "SessionManagerRange": "********/16", "AlarmRange": "********/16", "AudioRange": "********/16", "GPSLatitudeRange": "40.0-41.0", "GPSLongitudeRange": "-74.0--73.0"}',
         'IP ranges and GPS coordinates for anonymization'),
        ('FilePathPatterns', 'JSON',
         '{"DataPath": "C:\\AnonymizedData", "UNCPath": "\\\\anonymized-server\\share", "StreamProfile": "Profile", "BackupPath": "C:\\AnonymizedBackups", "LogPath": "C:\\AnonymizedLogs"}',
         'File path patterns for anonymization');

    PRINT 'DEFAULT CONFIGURATION LOADED';
    PRINT 'ALL MAGIC CONSTANTS FROM MODULES CENTRALIZED IN JSON CONFIGURATION';
END
ELSE
BEGIN
    PRINT 'Configuration table contains ' + CAST(@ConfigCount AS VARCHAR(10)) + ' existing entries';
END

-- =====================================================================================
-- STEP 6: CREATE MODERN TYPED CONFIGURATION ACCESSOR FUNCTIONS
-- =====================================================================================

PRINT 'Creating modern typed configuration accessor functions...';

-- String Configuration Accessor
IF OBJECT_ID('dbo.fn_GetConfigString', 'FN') IS NOT NULL
    DROP FUNCTION dbo.fn_GetConfigString;
GO

CREATE FUNCTION dbo.fn_GetConfigString(@ConfigKey VARCHAR(100))
RETURNS NVARCHAR(256)
WITH SCHEMABINDING
AS
BEGIN
    RETURN (
        SELECT StringValue
        FROM dbo.AnonymizationConfig
        WHERE ConfigKey = @ConfigKey AND DataType = 'STRING'
    );
END;
GO

-- Integer Configuration Accessor
IF OBJECT_ID('dbo.fn_GetConfigInt', 'FN') IS NOT NULL
    DROP FUNCTION dbo.fn_GetConfigInt;
GO

CREATE FUNCTION dbo.fn_GetConfigInt(@ConfigKey VARCHAR(100))
RETURNS INT
WITH SCHEMABINDING
AS
BEGIN
    RETURN (
        SELECT IntValue
        FROM dbo.AnonymizationConfig
        WHERE ConfigKey = @ConfigKey AND DataType = 'INT'
    );
END;
GO

-- Boolean Configuration Accessor
IF OBJECT_ID('dbo.fn_GetConfigBit', 'FN') IS NOT NULL
    DROP FUNCTION dbo.fn_GetConfigBit;
GO

CREATE FUNCTION dbo.fn_GetConfigBit(@ConfigKey VARCHAR(100))
RETURNS BIT
WITH SCHEMABINDING
AS
BEGIN
    RETURN (
        SELECT BitValue
        FROM dbo.AnonymizationConfig
        WHERE ConfigKey = @ConfigKey AND DataType = 'BIT'
    );
END;
GO

-- Decimal Configuration Accessor
IF OBJECT_ID('dbo.fn_GetConfigDecimal', 'FN') IS NOT NULL
    DROP FUNCTION dbo.fn_GetConfigDecimal;
GO

CREATE FUNCTION dbo.fn_GetConfigDecimal(@ConfigKey VARCHAR(100))
RETURNS DECIMAL(18,6)
WITH SCHEMABINDING
AS
BEGIN
    RETURN (
        SELECT DecimalValue
        FROM dbo.AnonymizationConfig
        WHERE ConfigKey = @ConfigKey AND DataType = 'DECIMAL'
    );
END;
GO

-- Date Configuration Accessor
IF OBJECT_ID('dbo.fn_GetConfigDate', 'FN') IS NOT NULL
    DROP FUNCTION dbo.fn_GetConfigDate;
GO

CREATE FUNCTION dbo.fn_GetConfigDate(@ConfigKey VARCHAR(100))
RETURNS DATETIME2
WITH SCHEMABINDING
AS
BEGIN
    RETURN (
        SELECT DateValue
        FROM dbo.AnonymizationConfig
        WHERE ConfigKey = @ConfigKey AND DataType = 'DATE'
    );
END;
GO

-- JSON Configuration Accessor
IF OBJECT_ID('dbo.fn_GetConfigJson', 'FN') IS NOT NULL
    DROP FUNCTION dbo.fn_GetConfigJson;
GO

CREATE FUNCTION dbo.fn_GetConfigJson(@ConfigKey VARCHAR(100))
RETURNS NVARCHAR(4000)
WITH SCHEMABINDING
AS
BEGIN
    RETURN (
        SELECT JsonValue
        FROM dbo.AnonymizationConfig
        WHERE ConfigKey = @ConfigKey AND DataType = 'JSON'
    );
END;
GO

-- =====================================================================================
-- STEP 7: CREATE JSON VALUE EXTRACTOR AND LEGACY COMPATIBILITY
-- =====================================================================================

-- JSON Value Extractor (for complex JSON configurations)
IF OBJECT_ID('dbo.fn_GetJsonConfigValue', 'FN') IS NOT NULL
    DROP FUNCTION dbo.fn_GetJsonConfigValue;
GO

CREATE FUNCTION dbo.fn_GetJsonConfigValue(@ConfigKey VARCHAR(100), @JsonPath VARCHAR(100))
RETURNS NVARCHAR(4000)
WITH SCHEMABINDING
AS
BEGIN
    DECLARE @Result NVARCHAR(4000);
    DECLARE @JsonData NVARCHAR(4000);
    
    -- Get JSON data using typed function
    SET @JsonData = dbo.fn_GetConfigJson(@ConfigKey);
    
    -- Extract JSON value if valid
    IF ISJSON(@JsonData) = 1
    BEGIN
        SET @Result = JSON_VALUE(@JsonData, @JsonPath);
    END
    
    RETURN @Result;
END;
GO

-- Legacy Compatibility Wrapper (for existing modules)
IF OBJECT_ID('dbo.fn_GetConfigValue', 'FN') IS NOT NULL
    DROP FUNCTION dbo.fn_GetConfigValue;
GO

CREATE FUNCTION dbo.fn_GetConfigValue(@ConfigKey VARCHAR(100))
RETURNS VARCHAR(MAX)
WITH SCHEMABINDING
AS
BEGIN
    DECLARE @Value VARCHAR(MAX);
    
    -- Check if new structure exists (has StringValue column)
    IF COL_LENGTH('dbo.AnonymizationConfig', 'StringValue') IS NOT NULL
    BEGIN
        -- New structure with typed columns
        SELECT @Value = COALESCE(StringValue, CAST(IntValue AS VARCHAR), CAST(BitValue AS VARCHAR), 
                                CAST(DecimalValue AS VARCHAR), CAST(DateValue AS VARCHAR), JsonValue)
        FROM dbo.AnonymizationConfig 
        WHERE ConfigKey = @ConfigKey;
    END
    ELSE
    BEGIN
        -- Legacy structure with ConfigValue column
        SELECT @Value = ConfigValue 
        FROM dbo.AnonymizationConfig 
        WHERE ConfigKey = @ConfigKey;
    END
    
    RETURN ISNULL(@Value, '');
END;
GO

-- =====================================================================================
-- STEP 8: CREATE CONFIGURATION MANAGEMENT PROCEDURES
-- =====================================================================================

-- Configuration Validation Function
IF OBJECT_ID('dbo.fn_ValidateConfigValue', 'FN') IS NOT NULL
    DROP FUNCTION dbo.fn_ValidateConfigValue;
GO

CREATE FUNCTION dbo.fn_ValidateConfigValue(
    @ConfigValue VARCHAR(MAX),
    @DataType VARCHAR(20)
)
RETURNS BIT
WITH SCHEMABINDING
AS
BEGIN
    DECLARE @IsValid BIT = 0;
    
    IF @DataType = 'STRING'
    BEGIN
        IF @ConfigValue IS NOT NULL AND LEN(@ConfigValue) <= 256
            SET @IsValid = 1;
    END
    ELSE IF @DataType = 'INT'
    BEGIN
        IF TRY_CAST(@ConfigValue AS INT) IS NOT NULL
            SET @IsValid = 1;
    END
    ELSE IF @DataType = 'BIT'
    BEGIN
        IF @ConfigValue IN ('0', '1', 'true', 'false', 'True', 'False')
            SET @IsValid = 1;
    END
    ELSE IF @DataType = 'DECIMAL'
    BEGIN
        IF TRY_CAST(@ConfigValue AS DECIMAL(18,6)) IS NOT NULL
            SET @IsValid = 1;
    END
    ELSE IF @DataType = 'DATE'
    BEGIN
        IF TRY_CAST(@ConfigValue AS DATETIME2) IS NOT NULL
            SET @IsValid = 1;
    END
    ELSE IF @DataType = 'JSON'
    BEGIN
        IF ISJSON(@ConfigValue) = 1
            SET @IsValid = 1;
    END
    
    RETURN @IsValid;
END;
GO

-- Configuration Validation Procedure
IF OBJECT_ID('dbo.sp_ValidateConfiguration', 'P') IS NOT NULL
    DROP PROCEDURE dbo.sp_ValidateConfiguration;
GO

CREATE PROCEDURE dbo.sp_ValidateConfiguration
AS
BEGIN
    SET NOCOUNT ON;
    
    PRINT 'Validating all configuration values...';
    PRINT '';
    
    DECLARE @ValidationErrors INT = 0;
    
    -- Validate modern structure
    SELECT 
        ConfigKey,
        DataType,
        CASE DataType
            WHEN 'STRING' THEN CASE WHEN StringValue IS NOT NULL AND LEN(StringValue) <= 256 THEN 'VALID' ELSE 'INVALID' END
            WHEN 'INT' THEN CASE WHEN IntValue IS NOT NULL THEN 'VALID' ELSE 'INVALID' END
            WHEN 'BIT' THEN CASE WHEN BitValue IS NOT NULL THEN 'VALID' ELSE 'INVALID' END
            WHEN 'DECIMAL' THEN CASE WHEN DecimalValue IS NOT NULL THEN 'VALID' ELSE 'INVALID' END
            WHEN 'DATE' THEN CASE WHEN DateValue IS NOT NULL THEN 'VALID' ELSE 'INVALID' END
            WHEN 'JSON' THEN CASE WHEN JsonValue IS NOT NULL AND ISJSON(JsonValue) = 1 THEN 'VALID' ELSE 'INVALID' END
            ELSE 'UNKNOWN TYPE'
        END AS ValidationStatus
    FROM dbo.AnonymizationConfig
    ORDER BY ConfigKey;
    
    SELECT @ValidationErrors = COUNT(*)
    FROM dbo.AnonymizationConfig
    WHERE (DataType = 'STRING' AND (StringValue IS NULL OR LEN(StringValue) > 256))
       OR (DataType = 'INT' AND IntValue IS NULL)
       OR (DataType = 'BIT' AND BitValue IS NULL)
       OR (DataType = 'DECIMAL' AND DecimalValue IS NULL)
       OR (DataType = 'DATE' AND DateValue IS NULL)
       OR (DataType = 'JSON' AND (JsonValue IS NULL OR ISJSON(JsonValue) = 0));
    
    PRINT '';
    IF @ValidationErrors = 0
        PRINT 'CONFIGURATION VALIDATION COMPLETED. ALL VALUES ARE VALID.';
    ELSE
        PRINT 'CONFIGURATION VALIDATION COMPLETED WITH ' + CAST(@ValidationErrors AS VARCHAR(10)) + ' ERRORS.';
END;
GO

-- Configuration Export Procedure
IF OBJECT_ID('dbo.sp_ExportConfiguration', 'P') IS NOT NULL
    DROP PROCEDURE dbo.sp_ExportConfiguration;
GO

CREATE PROCEDURE dbo.sp_ExportConfiguration
AS
BEGIN
    SET NOCOUNT ON;
    
    PRINT 'Configuration Export - Copy the statements below to recreate current configuration:';
    PRINT '==================================================================================';
    PRINT '';
    PRINT 'DELETE FROM dbo.AnonymizationConfig;';
    PRINT '';
    
    -- Export all configuration as INSERT statements
    SELECT 
        'INSERT INTO dbo.AnonymizationConfig (ConfigKey, DataType, ' +
        CASE DataType
            WHEN 'STRING' THEN 'StringValue'
            WHEN 'INT' THEN 'IntValue'
            WHEN 'BIT' THEN 'BitValue'
            WHEN 'DECIMAL' THEN 'DecimalValue'
            WHEN 'DATE' THEN 'DateValue'
            WHEN 'JSON' THEN 'JsonValue'
        END + ', Description) VALUES (''' + ConfigKey + ''', ''' + DataType + ''', ' +
        CASE DataType
            WHEN 'STRING' THEN CASE WHEN StringValue IS NOT NULL THEN '''' + REPLACE(StringValue, '''', '''''') + '''' ELSE 'NULL' END
            WHEN 'INT' THEN CASE WHEN IntValue IS NOT NULL THEN CAST(IntValue AS VARCHAR) ELSE 'NULL' END
            WHEN 'BIT' THEN CASE WHEN BitValue IS NOT NULL THEN CAST(BitValue AS VARCHAR) ELSE 'NULL' END
            WHEN 'DECIMAL' THEN CASE WHEN DecimalValue IS NOT NULL THEN CAST(DecimalValue AS VARCHAR) ELSE 'NULL' END
            WHEN 'DATE' THEN CASE WHEN DateValue IS NOT NULL THEN '''' + CONVERT(VARCHAR, DateValue, 120) + '''' ELSE 'NULL' END
            WHEN 'JSON' THEN CASE WHEN JsonValue IS NOT NULL THEN '''' + REPLACE(JsonValue, '''', '''''') + '''' ELSE 'NULL' END
        END + ', ' +
        CASE WHEN Description IS NOT NULL THEN '''' + REPLACE(Description, '''', '''''') + '''' ELSE 'NULL' END + ');' AS ExportSQL
    FROM dbo.AnonymizationConfig
    ORDER BY ConfigKey;
    
    PRINT '';
    PRINT 'Export completed.';
END;
GO

PRINT 'Modern typed configuration functions created';
PRINT 'Configuration management procedures created';

-- =====================================================================================
-- STEP 9: FINAL VALIDATION AND SUMMARY
-- =====================================================================================

PRINT '';
PRINT '=====================================================================================';
PRINT 'CONFIGURATION SYSTEM SETUP COMPLETED';
PRINT '=====================================================================================';

-- Show current configuration count
DECLARE @FinalConfigCount INT = (SELECT COUNT(*) FROM dbo.AnonymizationConfig);
PRINT 'Configuration entries loaded: ' + CAST(@FinalConfigCount AS VARCHAR(10));

-- Run validation
PRINT '';
PRINT 'Running final validation...';
EXEC dbo.sp_ValidateConfiguration;

PRINT '';
PRINT '=== AVAILABLE CONFIGURATION FUNCTIONS ===';
PRINT '';
PRINT 'MODERN TYPED FUNCTIONS (RECOMMENDED):';
PRINT '- dbo.fn_GetConfigString(@Key)    - Get string values';
PRINT '- dbo.fn_GetConfigInt(@Key)       - Get integer values';  
PRINT '- dbo.fn_GetConfigBit(@Key)       - Get boolean values';
PRINT '- dbo.fn_GetConfigDecimal(@Key)   - Get decimal values';
PRINT '- dbo.fn_GetConfigDate(@Key)      - Get date values';
PRINT '- dbo.fn_GetConfigJson(@Key)      - Get JSON values';
PRINT '';
PRINT 'JSON EXTRACTION:';
PRINT '- dbo.fn_GetJsonConfigValue(@Key, @JsonPath) - Extract JSON values';
PRINT '';
PRINT 'LEGACY COMPATIBILITY:';
PRINT '- dbo.fn_GetConfigValue(@Key)     - Universal accessor (legacy modules)';
PRINT '';
PRINT '=== CONFIGURATION MANAGEMENT PROCEDURES ===';
PRINT '';
PRINT '- EXEC dbo.sp_ValidateConfiguration       - Validate all config values';  
PRINT '- EXEC dbo.sp_ExportConfiguration         - Export current config to script';
PRINT '';
PRINT '=== USAGE EXAMPLES ===';
PRINT '';
PRINT 'Modern typed access (RECOMMENDED):';
PRINT '  DECLARE @BatchSize INT = dbo.fn_GetConfigInt(''BatchSize'');';
PRINT '  DECLARE @DryRun BIT = dbo.fn_GetConfigBit(''DryRun'');';
PRINT '  DECLARE @Domain NVARCHAR(256) = dbo.fn_GetConfigString(''AnonymizedDomain'');';
PRINT '';
PRINT 'Centralized constants access:';
PRINT '  DECLARE @CameraPrefix NVARCHAR(50) = dbo.fn_GetJsonConfigValue(''AnonymizationPatterns'', ''$.CameraPrefix'');';
PRINT '  DECLARE @CameraIPRange NVARCHAR(20) = dbo.fn_GetJsonConfigValue(''IPRanges'', ''$.CameraRange'');';
PRINT '';
PRINT 'Legacy compatibility (for existing modules):';
PRINT '  DECLARE @LegacyValue VARCHAR(MAX) = dbo.fn_GetConfigValue(''SomeKey'');';
PRINT '';
PRINT '=== PERFORMANCE BENEFITS ACHIEVED ===';
PRINT '';
PRINT 'ELIMINATED VARCHAR(MAX) COLUMNS FOR BETTER ROW DENSITY';
PRINT 'ADDED STRONGLY-TYPED COLUMNS WITH PROPER DATA TYPES';
PRINT 'REMOVED STRING-BASED TYPE CONVERSION OVERHEAD';
PRINT 'ADDED JSON SUPPORT FOR COMPLEX CONFIGURATIONS';
PRINT 'ADDED PROPER CONSTRAINTS AND INDEXING';
PRINT 'CENTRALIZED ALL MAGIC CONSTANTS FROM MODULES';
PRINT 'CLEAN FUNCTION SIGNATURES WITH TRUE TYPE SAFETY';
PRINT '';
PRINT '=== CENTRALIZED CONSTANTS NOW AVAILABLE ===';
PRINT '';
PRINT 'All magic constants from anonymization modules are now centralized:';
PRINT '- Camera prefixes, user prefixes, server hostnames';
PRINT '- IP ranges for cameras, servers, alarms, audio devices';  
PRINT '- Domain names and email patterns';
PRINT '- File paths and UNC paths';
PRINT '- GPS coordinate ranges';
PRINT '';
PRINT 'CONFIGURATION SYSTEM READY FOR OPERATIONS!';
PRINT '=====================================================================================';