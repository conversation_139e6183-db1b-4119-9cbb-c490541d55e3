-- =====================================================================================
-- COMPLETEVIEW DATABASE ANONYMIZATION - STANDALONE MODULE
-- File: 04_User_Account_Anonymization.sql

-- =====================================================================================
-- This is a standalone, pre-configured SQL module that can be executed directly
-- in SQL Server Management Studio without requiring PowerShell configuration.
-- 
-- IMPORTANT: 
-- 1. BACKUP YOUR DATABASE BEFORE RUNNING THIS SCRIPT
-- 2. Review the configuration values below before execution
-- 3. Execute modules in numerical order (01, 02, 03, etc.)
-- =====================================================================================

SET NOCOUNT ON;
SET XACT_ABORT ON;

-- =====================================================================================
-- CENTRALIZED CONFIGURATION LOADER
-- =====================================================================================

-- Verify configuration prerequisites
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationConfig')
BEGIN
    THROW 50001, 'AnonymizationConfig table not found. Please run Module 01 (Setup and Validation) first.', 1;
END

IF OBJECT_ID('dbo.fn_GetConfigValue') IS NULL
BEGIN
    THROW 50002, 'Configuration helper function fn_GetConfigValue not found. Please run Module 01 (Setup and Validation) first.', 1;
END

-- Load configuration values from centralized table
DECLARE @ExecutionID VARCHAR(50) = 'MODULE04_' + REPLACE(CAST(NEWID() AS VARCHAR(36)), '-', '');
DECLARE @ModuleName VARCHAR(100) = 'User Account Anonymization';

PRINT 'Starting ' + @ModuleName + '...';
PRINT 'Execution ID: ' + @ExecutionID;

DECLARE @AnonymizationSeed INT = CAST(dbo.fn_GetConfigValue('AnonymizationSeed') AS INT);
DECLARE @AnonymizedDomain NVARCHAR(100) = dbo.fn_GetConfigValue('AnonymizedDomain');
DECLARE @EmailSuffix NVARCHAR(100) = dbo.fn_GetConfigValue('EmailSuffix');
DECLARE @DefaultPassword NVARCHAR(100) = dbo.fn_GetConfigValue('DefaultPassword');
DECLARE @BatchSize INT = CAST(dbo.fn_GetConfigValue('BatchSize') AS INT);
DECLARE @DryRun BIT = CAST(dbo.fn_GetConfigValue('DryRun') AS BIT);
DECLARE @CommitChanges BIT = CAST(dbo.fn_GetConfigValue('CommitChanges') AS BIT);
DECLARE @UsePerformanceOptimizations BIT = CAST(dbo.fn_GetConfigValue('UsePerformanceOptimizations') AS BIT);
DECLARE @RequireRecentBackup BIT = CAST(dbo.fn_GetConfigValue('RequireRecentBackup') AS BIT);
DECLARE @BackupMaxAgeDays INT = CAST(dbo.fn_GetConfigValue('BackupMaxAgeDays') AS INT);

-- IP and path configuration
DECLARE @CVE_IPRange_Cameras NVARCHAR(20) = dbo.fn_GetConfigValue('CVE_IPRange_Cameras');
DECLARE @CVE_IPRange_Servers NVARCHAR(20) = dbo.fn_GetConfigValue('CVE_IPRange_Servers');
DECLARE @CVE_IPRange_SessionManager NVARCHAR(20) = dbo.fn_GetConfigValue('CVE_IPRange_SessionManager');
DECLARE @CVE_FilePath_Prefix NVARCHAR(100) = dbo.fn_GetConfigValue('CVE_FilePath_Prefix');
DECLARE @CVE_UNCPath_Prefix NVARCHAR(100) = dbo.fn_GetConfigValue('CVE_UNCPath_Prefix');
DECLARE @CVE_StreamProfile_Prefix NVARCHAR(50) = dbo.fn_GetConfigValue('CVE_StreamProfile_Prefix');

-- User-specific configuration
DECLARE @UserTablePrefix NVARCHAR(20) = ISNULL(dbo.fn_GetConfigValue('UserTablePrefix'), 'User');
DECLARE @GroupTablePrefix NVARCHAR(20) = ISNULL(dbo.fn_GetConfigValue('GroupTablePrefix'), 'Group');
DECLARE @SessionIPRange NVARCHAR(20) = @CVE_IPRange_SessionManager;

PRINT 'Configuration loaded from centralized table for User Account Anonymization';

-- =====================================================================================
-- MODULE 04: USER ACCOUNT ANONYMIZATION
-- =====================================================================================
-- Description: Anonymizes user account data including usernames, passwords,
--              personal information, and authentication details
-- =====================================================================================

PRINT '=======================================================================';
PRINT 'MODULE 04: USER ACCOUNT ANONYMIZATION';
PRINT '=======================================================================';

BEGIN TRY
    BEGIN TRAN;

    -- Create pre-anonymization snapshot
    PRINT 'Creating pre-anonymization snapshot...';
    IF OBJECT_ID('dbo.sp_CreatePreAnonymizationSnapshot') IS NOT NULL
    BEGIN
        EXEC dbo.sp_CreatePreAnonymizationSnapshot 
            @ExecutionID = @ExecutionID,
            @ModuleName = @ModuleName;
    END

    -- Initialize module variables
    DECLARE @RowsAffected INT;
    DECLARE @TotalRowsProcessed INT = 0;
    DECLARE @ModuleStartTime DATETIME = GETDATE();
    DECLARE @OperationStart DATETIME;

    PRINT 'Execution Mode: ' + CASE WHEN @DryRun = 1 THEN 'DRY RUN (Preview Only)' ELSE 'LIVE EXECUTION' END;
    PRINT '';

    -- =====================================================================================
    -- USER TABLE ANONYMIZATION
    -- =====================================================================================
    PRINT 'Processing User table...';
    SET @OperationStart = GETDATE();

    -- Check if User table exists
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'User')
    BEGIN
        DECLARE @UserRowCount INT = (SELECT COUNT(*) FROM dbo.[User]);
        
        IF @UserRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@UserRowCount AS VARCHAR) + ' user records to process.';
            
            -- Preview changes
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: User data anonymization preview:';
                SELECT TOP 10
                    UserID,
                    @UserTablePrefix + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(UserID, @AnonymizationSeed, 'USER') AS VARCHAR), 6) AS New_Username,
                    CASE 
                        WHEN @DefaultPassword = '[ACCOUNT_DISABLED]' THEN '[ACCOUNT_DISABLED]'
                        WHEN @DefaultPassword = '[SALTED_HASH]' THEN '[SALTED_HASH_GENERATED]'
                        WHEN @DefaultPassword IS NULL THEN NULL
                        ELSE '[REDACTED]'
                    END AS New_Password,
                    'User ' + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(UserID, @AnonymizationSeed, 'USER') AS VARCHAR), 6) AS New_FullName,
                    @UserTablePrefix + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(UserID, @AnonymizationSeed, 'USER') AS VARCHAR), 6) + '@example.com' AS New_Email,
                    '[REDACTED]' AS New_PersonalInfo
                FROM dbo.[User]
                WHERE Username IS NOT NULL OR Password IS NOT NULL OR FullName IS NOT NULL;
                
                SET @RowsAffected = @UserRowCount;
            END
            ELSE
            BEGIN
                -- Anonymize usernames using CTE
                WITH UsernameCTE AS (
                    SELECT UserID, 
                           @UserTablePrefix + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(UserID, @AnonymizationSeed, 'USER') AS VARCHAR), 6) AS NewUsername
                    FROM dbo.[User] 
                    WHERE Username IS NOT NULL
                )
                UPDATE u 
                SET Username = cte.NewUsername
                FROM dbo.[User] u
                INNER JOIN UsernameCTE cte ON u.UserID = cte.UserID;
                
                DECLARE @UsernameUpdateCount INT = @@ROWCOUNT;
                
                -- Anonymize passwords using secure strategy
                DECLARE @PasswordUpdateCount INT = 0;
                IF @DefaultPassword = '[ACCOUNT_DISABLED]'
                BEGIN
                    UPDATE dbo.[User] 
                    SET Password = '[ACCOUNT_DISABLED]'
                    WHERE Password IS NOT NULL;
                    SET @PasswordUpdateCount = @@ROWCOUNT;
                END
                ELSE IF @DefaultPassword = '[SALTED_HASH]'
                BEGIN
                    UPDATE dbo.[User] 
                    SET Password = '$2b$12$' + REPLACE(CAST(NEWID() AS VARCHAR(36)), '-', '') + 'UserHash'
                    WHERE Password IS NOT NULL;
                    SET @PasswordUpdateCount = @@ROWCOUNT;
                END
                ELSE IF @DefaultPassword IS NULL
                BEGIN
                    UPDATE dbo.[User] 
                    SET Password = NULL
                    WHERE Password IS NOT NULL;
                    SET @PasswordUpdateCount = @@ROWCOUNT;
                END
                ELSE
                BEGIN
                    UPDATE dbo.[User] 
                    SET Password = @DefaultPassword
                    WHERE Password IS NOT NULL;
                    SET @PasswordUpdateCount = @@ROWCOUNT;
                END
                
                -- Anonymize full names using CTE
                WITH FullNameCTE AS (
                    SELECT UserID,
                           'User ' + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(UserID, @AnonymizationSeed, 'USER') AS VARCHAR), 6) AS NewFullName
                    FROM dbo.[User] 
                    WHERE FullName IS NOT NULL
                )
                UPDATE u 
                SET FullName = cte.NewFullName
                FROM dbo.[User] u
                INNER JOIN FullNameCTE cte ON u.UserID = cte.UserID;
                
                DECLARE @FullNameUpdateCount INT = @@ROWCOUNT;
                
                -- Anonymize email addresses using CTE
                WITH EmailCTE AS (
                    SELECT UserID,
                           @UserTablePrefix + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(UserID, @AnonymizationSeed, 'USER') AS VARCHAR), 6) + '@example.com' AS NewEmail
                    FROM dbo.[User] 
                    WHERE Email IS NOT NULL
                )
                UPDATE u 
                SET Email = cte.NewEmail
                FROM dbo.[User] u
                INNER JOIN EmailCTE cte ON u.UserID = cte.UserID;
                
                DECLARE @EmailUpdateCount INT = @@ROWCOUNT;
                
                -- Anonymize first and last names using CTE
                WITH NameCTE AS (
                    SELECT UserID,
                           'FirstName' + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(UserID, @AnonymizationSeed, 'FNAME') AS VARCHAR), 6) AS NewFirstName,
                           'LastName' + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(UserID, @AnonymizationSeed, 'LNAME') AS VARCHAR), 6) AS NewLastName
                    FROM dbo.[User] 
                    WHERE FirstName IS NOT NULL OR LastName IS NOT NULL
                )
                UPDATE u 
                SET FirstName = cte.NewFirstName,
                    LastName = cte.NewLastName
                FROM dbo.[User] u
                INNER JOIN NameCTE cte ON u.UserID = cte.UserID;
                
                DECLARE @NameUpdateCount INT = @@ROWCOUNT;
                
                -- Anonymize phone numbers
                UPDATE dbo.[User] 
                SET PhoneNumber = '555-' + RIGHT('000' + CAST((UserID % 999) + 100 AS VARCHAR), 3) + '-' + RIGHT('0000' + CAST((UserID % 9999) + 1000 AS VARCHAR), 4)
                WHERE PhoneNumber IS NOT NULL;
                
                DECLARE @PhoneUpdateCount INT = @@ROWCOUNT;
                
                SET @RowsAffected = @UsernameUpdateCount + @PasswordUpdateCount + @FullNameUpdateCount + @EmailUpdateCount + @NameUpdateCount + @PhoneUpdateCount;
                
                -- Buffer operations for aggregated logging
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES 
                    ('User Data', 'User', 'Username', @UsernameUpdateCount, 'Sequential Naming', @OperationStart, GETDATE(),
                     'Usernames anonymized with ' + @UserTablePrefix + '#### pattern'),
                    ('User Data', 'User', 'Password', @PasswordUpdateCount, 'Secure Strategy', @OperationStart, GETDATE(),
                     'User passwords processed using strategy: ' + ISNULL(@DefaultPassword, 'NULL')),
                    ('User Data', 'User', 'FullName', @FullNameUpdateCount, 'Sequential Naming', @OperationStart, GETDATE(),
                     'Full names anonymized'),
                    ('User Data', 'User', 'Email', @EmailUpdateCount, 'Sequential Naming', @OperationStart, GETDATE(),
                     'Email addresses anonymized'),
                    ('User Data', 'User', 'Names', @NameUpdateCount, 'Sequential Naming', @OperationStart, GETDATE(),
                     'First and last names anonymized'),
                    ('User Data', 'User', 'PhoneNumber', @PhoneUpdateCount, 'Pattern Replacement', @OperationStart, GETDATE(),
                     'Phone numbers anonymized');
            END
            
            PRINT 'User table anonymized. Rows affected: ' + CAST(@RowsAffected AS VARCHAR);
        END
        ELSE
        BEGIN
            PRINT 'No user records found to anonymize.';
            SET @RowsAffected = 0;
        END
    END
    ELSE
    BEGIN
        PRINT 'User table not found in database.';
        SET @RowsAffected = 0;
    END

    SET @TotalRowsProcessed = ISNULL(@RowsAffected, 0);

    -- =====================================================================================
    -- USER GROUPS AND ROLES
    -- =====================================================================================
    PRINT '';
    PRINT 'Processing user groups and roles...';

    -- Anonymize UserGroup table
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'UserGroup')
    BEGIN
        DECLARE @UserGroupRowCount INT = (SELECT COUNT(*) FROM dbo.UserGroup);
        SET @OperationStart = GETDATE();
        
        IF @UserGroupRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@UserGroupRowCount AS VARCHAR) + ' user group records to process.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: User group anonymization preview:';
                SELECT TOP 10
                    GroupID,
                    @GroupTablePrefix + RIGHT('000' + CAST(ROW_NUMBER() OVER (ORDER BY GroupID) AS VARCHAR), 3) AS New_GroupName,
                    'Anonymized user group' AS New_Description
                FROM dbo.UserGroup;
                
                SET @RowsAffected = @UserGroupRowCount;
            END
            ELSE
            BEGIN
                -- Anonymize group names using CTE
                WITH GroupCTE AS (
                    SELECT GroupID,
                           @GroupTablePrefix + RIGHT('000' + CAST(ROW_NUMBER() OVER (ORDER BY GroupID) AS VARCHAR), 3) AS NewGroupName
                    FROM dbo.UserGroup 
                    WHERE GroupName IS NOT NULL
                )
                UPDATE ug 
                SET GroupName = cte.NewGroupName,
                    Description = 'Anonymized user group'
                FROM dbo.UserGroup ug
                INNER JOIN GroupCTE cte ON ug.GroupID = cte.GroupID;
                
                DECLARE @GroupUpdateCount INT = @@ROWCOUNT;
                SET @RowsAffected = @GroupUpdateCount;
                
                -- Buffer operation for aggregated logging
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('User Data', 'UserGroup', 'GroupName', @GroupUpdateCount, 'Sequential Naming', @OperationStart, GETDATE(),
                        'User group names anonymized with ' + @GroupTablePrefix + '### pattern');
                
                PRINT 'Updated ' + CAST(@GroupUpdateCount AS VARCHAR) + ' user group records';
            END
            
            SET @TotalRowsProcessed = @TotalRowsProcessed + @RowsAffected;
        END
        ELSE
        BEGIN
            PRINT 'No user group records found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'UserGroup table not found - skipping user group anonymization';
    END

    -- =====================================================================================
    -- USER LOGIN AND SESSION DATA
    -- =====================================================================================
    PRINT '';
    PRINT 'Processing user login and session data...';

    -- Anonymize UserLogin table (session and authentication data)
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'UserLogin')
    BEGIN
        DECLARE @UserLoginRowCount INT = (SELECT COUNT(*) FROM dbo.UserLogin);
        SET @OperationStart = GETDATE();
        
        IF @UserLoginRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@UserLoginRowCount AS VARCHAR) + ' user login records to process.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: User login data anonymization preview:';
                SELECT TOP 10
                    UserLoginId,
                    MachineName AS Current_MachineName,
                    'anonymized-client.example.com' AS New_MachineName,
                    UserAccessToken AS Current_AccessToken,
                    NEWID() AS New_AccessToken,
                    UserName AS Current_UserName,
                    CONCAT('User', RIGHT('000' + CAST(ROW_NUMBER() OVER (ORDER BY UserLoginId) AS VARCHAR), 3)) AS New_UserName
                FROM dbo.UserLogin
                WHERE MachineName IS NOT NULL OR UserAccessToken IS NOT NULL;
                
                SET @RowsAffected = @UserLoginRowCount;
            END
            ELSE
            BEGIN
                -- Anonymize login session data
                WITH UserLoginCTE AS (
                    SELECT UserLoginId,
                           CONCAT('User', RIGHT('000' + CAST(ROW_NUMBER() OVER (ORDER BY UserLoginId) AS VARCHAR), 3)) AS NewUserName
                    FROM dbo.UserLogin 
                    WHERE UserName IS NOT NULL
                )
                UPDATE ul 
                SET UserName = cte.NewUserName
                FROM dbo.UserLogin ul
                INNER JOIN UserLoginCTE cte ON ul.UserLoginId = cte.UserLoginId;

                -- Anonymize machine names and access tokens
                UPDATE dbo.UserLogin 
                SET MachineName = 'anonymized-client.example.com',
                    UserAccessToken = NEWID(),
                    Password = CASE 
                        WHEN @DefaultPassword = '[ACCOUNT_DISABLED]' THEN '[ACCOUNT_DISABLED]'
                        WHEN @DefaultPassword = '[SALTED_HASH]' THEN '$2b$12$' + REPLACE(CAST(NEWID() AS VARCHAR(36)), '-', '') + 'LoginHash'
                        WHEN @DefaultPassword IS NULL THEN NULL
                        ELSE '[REDACTED-PASSWORD]'
                    END
                WHERE MachineName IS NOT NULL OR UserAccessToken IS NOT NULL OR Password IS NOT NULL;
                
                DECLARE @LoginUpdateCount INT = @@ROWCOUNT;
                SET @RowsAffected = @LoginUpdateCount;
                
                -- Buffer operation for aggregated logging
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('User Data', 'UserLogin', 'SessionData', @LoginUpdateCount, 'Pattern Replacement', @OperationStart, GETDATE(),
                        'User login session data and access tokens anonymized');
                
                PRINT 'Updated ' + CAST(@LoginUpdateCount AS VARCHAR) + ' user login records';
            END
            
            SET @TotalRowsProcessed = @TotalRowsProcessed + @RowsAffected;
        END
        ELSE
        BEGIN
            PRINT 'No user login records found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'UserLogin table not found - skipping login session data anonymization';
    END

    -- =====================================================================================
    -- AUTHENTICATION LOGS
    -- =====================================================================================
    PRINT '';
    PRINT 'Processing authentication logs...';

    -- Anonymize authentication and audit logs
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AuthenticationLog')
    BEGIN
        DECLARE @AuthLogRowCount INT = (SELECT COUNT(*) FROM dbo.AuthenticationLog);
        SET @OperationStart = GETDATE();
        
        IF @AuthLogRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@AuthLogRowCount AS VARCHAR) + ' authentication log records to process.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: Authentication log anonymization preview:';
                SELECT TOP 10
                    LogID,
                    'anonymized-user' AS New_Username,
                    @SessionIPRange + '.' + CAST((LogID % 253) + 1 AS VARCHAR) AS New_SourceIP,
                    '[REDACTED]' AS New_UserAgent,
                    'Authentication attempt (details redacted)' AS New_Details
                FROM dbo.AuthenticationLog;
                
                SET @RowsAffected = @AuthLogRowCount;
            END
            ELSE
            BEGIN
                -- Anonymize authentication logs with sequential IP assignment
                WITH AuthLogCTE AS (
                    SELECT LogID,
                           ROW_NUMBER() OVER (ORDER BY LogID) AS RowNum
                    FROM dbo.AuthenticationLog
                    WHERE Username IS NOT NULL OR SourceIP IS NOT NULL
                )
                UPDATE dbo.AuthenticationLog 
                SET Username = 'anonymized-user',
                    SourceIP = @SessionIPRange + '.' + 
                        CAST((AuthLogCTE.RowNum - 1) / 254 + 1 AS VARCHAR) + '.' + 
                        CAST((AuthLogCTE.RowNum - 1) % 254 + 1 AS VARCHAR),
                    UserAgent = '[REDACTED]',
                    Details = 'Authentication attempt (details redacted)'
                FROM dbo.AuthenticationLog
                INNER JOIN AuthLogCTE ON dbo.AuthenticationLog.LogID = AuthLogCTE.LogID;
                
                DECLARE @AuthUpdateCount INT = @@ROWCOUNT;
                SET @RowsAffected = @AuthUpdateCount;
                
                -- Buffer operation for aggregated logging
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('User Data', 'AuthenticationLog', 'LogData', @AuthUpdateCount, 'Pattern Replacement', @OperationStart, GETDATE(),
                        'Authentication logs anonymized with sequential IPs in ' + @SessionIPRange + '.x.x range');
                
                PRINT 'Updated ' + CAST(@AuthUpdateCount AS VARCHAR) + ' authentication log records';
            END
            
            SET @TotalRowsProcessed = @TotalRowsProcessed + @RowsAffected;
        END
        ELSE
        BEGIN
            PRINT 'No authentication log records found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'AuthenticationLog table not found - skipping authentication log anonymization';
    END

    -- =====================================================================================
    -- MODULE COMPLETION
    -- =====================================================================================
    DECLARE @ModuleEndTime DATETIME = GETDATE();
    DECLARE @ModuleExecutionTime INT = DATEDIFF(MILLISECOND, @ModuleStartTime, @ModuleEndTime);

    -- Flush aggregated logging buffer to reduce transaction log bloat
    IF OBJECT_ID('dbo.sp_FlushAnonymizationBuffer') IS NOT NULL
    BEGIN
        EXEC dbo.sp_FlushAnonymizationBuffer @DryRun = @DryRun;
    END

    -- Log module completion
    INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, ColumnName, RowsAffected, AnonymizationType, ExecutionTimeMs, BatchProcessed, Notes)
    VALUES ('User Data', 'MODULE_COMPLETE', 'ALL', @TotalRowsProcessed, 'Module Execution', 
            @ModuleExecutionTime, 0, 
            'User account anonymization completed in ' + CAST(DATEDIFF(SECOND, @ModuleStartTime, @ModuleEndTime) AS VARCHAR) + ' seconds');

    PRINT '';
    PRINT '=======================================================================';
    PRINT 'USER ACCOUNT ANONYMIZATION MODULE COMPLETED';
    PRINT '=======================================================================';
    PRINT 'Total execution time: ' + CAST(DATEDIFF(SECOND, @ModuleStartTime, @ModuleEndTime) AS VARCHAR) + ' seconds';
    PRINT 'Total rows processed: ' + CAST(@TotalRowsProcessed AS VARCHAR);
    PRINT 'Operations completed: User accounts, groups, login sessions, authentication logs';
    PRINT 'Dry run mode: ' + CASE WHEN @DryRun = 1 THEN 'YES (No data modified)' ELSE 'NO (Data modified)' END;
    PRINT '=======================================================================';

    -- Create post-anonymization snapshot
    IF @DryRun = 0 AND OBJECT_ID('dbo.sp_CreatePostAnonymizationSnapshot') IS NOT NULL
    BEGIN
        PRINT 'Creating post-anonymization snapshot...';
        EXEC dbo.sp_CreatePostAnonymizationSnapshot 
            @ExecutionID = @ExecutionID,
            @ModuleName = @ModuleName;
    END

    IF @DryRun = 1 OR @CommitChanges = 0 
        ROLLBACK TRAN 
    ELSE 
        COMMIT TRAN;

END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0 ROLLBACK TRAN;
    
    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();
    
    PRINT 'ERROR in User Account Anonymization: ' + @ErrorMessage;
    
    -- Log the error if possible
    IF OBJECT_ID('dbo.AnonymizationLog') IS NOT NULL
    BEGIN
        INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, ColumnName, RowsAffected, AnonymizationType, Notes)
        VALUES ('User Data', 'ERROR', 'MODULE_FAILURE', 0, 'Error', @ErrorMessage);
    END
    
    RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
END CATCH