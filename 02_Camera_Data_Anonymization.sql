-- =====================================================================================
-- COMPLETEVIEW DATABASE ANONYMIZATION - STANDALONE MODULE
-- File: 02_Camera_Data_Anonymization.sql
-- =====================================================================================
-- This is a standalone, pre-configured SQL module that can be executed directly
-- in SQL Server Management Studio without requiring PowerShell configuration.
-- 
-- IMPORTANT: 
-- 1. BACKUP YOUR DATABASE BEFORE RUNNING THIS SCRIPT
-- 2. Review the configuration values below before execution
-- 3. Execute modules in numerical order (01, 02, 03, etc.)
-- 
-- LOGGING OPTIMIZATION:
-- This module uses aggregated logging to minimize transaction log bloat.
-- Individual operations are buffered and flushed at module completion.
-- =====================================================================================

SET NOCOUNT ON;
SET XACT_ABORT ON;

-- =====================================================================================
-- CONFIGURATION LOADING
-- =====================================================================================

-- Verify configuration table exists
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationConfig')
BEGIN
    THROW 50001, 'AnonymizationConfig table not found. Please run Module 01 (Setup and Validation) first.', 1;
END

-- Verify configuration helper function exists
IF OBJECT_ID('dbo.fn_GetConfigValue') IS NULL
BEGIN
    THROW 50002, 'Configuration helper function fn_GetConfigValue not found. Please run Module 01 (Setup and Validation) first.', 1;
END

-- Load all configuration values from centralized table
DECLARE @AnonymizationSeed INT = CAST(dbo.fn_GetConfigValue('AnonymizationSeed') AS INT);
DECLARE @AnonymizedDomain NVARCHAR(100) = dbo.fn_GetConfigValue('AnonymizedDomain');
DECLARE @EmailSuffix NVARCHAR(100) = dbo.fn_GetConfigValue('EmailSuffix');
DECLARE @DefaultPassword NVARCHAR(100) = dbo.fn_GetConfigValue('DefaultPassword');
DECLARE @BatchSize INT = CAST(dbo.fn_GetConfigValue('BatchSize') AS INT);
DECLARE @DryRun BIT = CAST(dbo.fn_GetConfigValue('DryRun') AS BIT);
DECLARE @CommitChanges BIT = CAST(dbo.fn_GetConfigValue('CommitChanges') AS BIT);
DECLARE @UsePerformanceOptimizations BIT = CAST(dbo.fn_GetConfigValue('UsePerformanceOptimizations') AS BIT);
DECLARE @RequireRecentBackup BIT = CAST(dbo.fn_GetConfigValue('RequireRecentBackup') AS BIT);
DECLARE @BackupMaxAgeDays INT = CAST(dbo.fn_GetConfigValue('BackupMaxAgeDays') AS INT);

-- Path and IP configuration
DECLARE @CVE_IPRange_Cameras NVARCHAR(20) = dbo.fn_GetConfigValue('CVE_IPRange_Cameras');
DECLARE @CVE_IPRange_Servers NVARCHAR(20) = dbo.fn_GetConfigValue('CVE_IPRange_Servers');
DECLARE @CVE_IPRange_SessionManager NVARCHAR(20) = dbo.fn_GetConfigValue('CVE_IPRange_SessionManager');
DECLARE @CVE_FilePath_Prefix NVARCHAR(100) = dbo.fn_GetConfigValue('CVE_FilePath_Prefix');
DECLARE @CVE_UNCPath_Prefix NVARCHAR(100) = dbo.fn_GetConfigValue('CVE_UNCPath_Prefix');
DECLARE @CVE_StreamProfile_Prefix NVARCHAR(50) = dbo.fn_GetConfigValue('CVE_StreamProfile_Prefix');

-- GPS coordinates
DECLARE @GPSMinLatitude DECIMAL(10,6) = CAST(dbo.fn_GetConfigValue('GPSMinLatitude') AS DECIMAL(10,6));
DECLARE @GPSMaxLatitude DECIMAL(10,6) = CAST(dbo.fn_GetConfigValue('GPSMaxLatitude') AS DECIMAL(10,6));
DECLARE @GPSMinLongitude DECIMAL(10,6) = CAST(dbo.fn_GetConfigValue('GPSMinLongitude') AS DECIMAL(10,6));
DECLARE @GPSMaxLongitude DECIMAL(10,6) = CAST(dbo.fn_GetConfigValue('GPSMaxLongitude') AS DECIMAL(10,6));

-- Set default GPS coordinates if not configured
IF @GPSMinLatitude IS NULL SET @GPSMinLatitude = 40.000000;
IF @GPSMaxLatitude IS NULL SET @GPSMaxLatitude = 45.000000;
IF @GPSMinLongitude IS NULL SET @GPSMinLongitude = -125.000000;
IF @GPSMaxLongitude IS NULL SET @GPSMaxLongitude = -70.000000;

-- Module identification
DECLARE @ModuleName VARCHAR(100) = 'Camera Data Anonymization';
DECLARE @ExecutionID VARCHAR(50) = 'MODULE02_' + REPLACE(CAST(NEWID() AS VARCHAR(36)), '-', '');

PRINT 'Configuration loaded from centralized table:';
PRINT CONCAT('  AnonymizationSeed: ', @AnonymizationSeed);
PRINT CONCAT('  DryRun: ', CASE WHEN @DryRun = 1 THEN 'ENABLED' ELSE 'DISABLED' END);

-- =====================================================================================
-- CREATE HELPER FUNCTIONS
-- =====================================================================================

-- Create anonymization hash function
IF OBJECT_ID('dbo.fn_AnonymizationHash') IS NOT NULL
    DROP FUNCTION dbo.fn_AnonymizationHash;
GO

CREATE FUNCTION dbo.fn_AnonymizationHash(@InputValue INT, @Seed INT, @Salt VARCHAR(10))
RETURNS INT
AS
BEGIN
    RETURN ABS(CHECKSUM(@InputValue, @Seed, @Salt));
END;
GO

-- Create anonymized sequence function
IF OBJECT_ID('dbo.fn_GetAnonymizedSequence') IS NOT NULL
    DROP FUNCTION dbo.fn_GetAnonymizedSequence;
GO

CREATE FUNCTION dbo.fn_GetAnonymizedSequence(@InputValue INT, @Seed INT, @Type VARCHAR(10))
RETURNS INT
AS
BEGIN
    RETURN (ABS(CHECKSUM(@InputValue, @Seed, @Type)) % 999999) + 1;
END;
GO

-- =====================================================================================
-- CAMERA DATA ANONYMIZATION MODULE
-- =====================================================================================

PRINT '=======================================================================';
PRINT 'CAMERA DATA ANONYMIZATION MODULE';
PRINT '=======================================================================';

BEGIN TRY
    BEGIN TRAN;

    -- Initialize module variables
    DECLARE @RowsAffected INT;
    DECLARE @TotalRowsProcessed INT = 0;
    DECLARE @ModuleStartTime DATETIME = GETDATE();
    DECLARE @OperationStart DATETIME;

    -- =====================================================================================
    -- CREATE PRE-ANONYMIZATION SNAPSHOT FOR VERIFICATION
    -- =====================================================================================
    PRINT 'Creating pre-anonymization snapshot for verification...';

    IF OBJECT_ID('dbo.sp_CreatePreAnonymizationSnapshot') IS NOT NULL
    BEGIN
        EXEC dbo.sp_CreatePreAnonymizationSnapshot 
            @ExecutionID = @ExecutionID,
            @ModuleName = @ModuleName;
    END
    ELSE
    BEGIN
        PRINT 'Snapshot procedures not available. Proceeding without snapshot verification.';
    END

    PRINT 'Camera anonymization started at: ' + CONVERT(VARCHAR, @ModuleStartTime, 121);
    PRINT 'Dry Run Mode: ' + CASE WHEN @DryRun = 1 THEN 'ENABLED' ELSE 'DISABLED' END;
    PRINT '';

    -- =====================================================================================
    -- 1. ANONYMIZE CAMERA NAMES
    -- =====================================================================================
    PRINT 'Anonymizing camera names...';
    SET @OperationStart = GETDATE();

    DECLARE @CameraBatchCount INT = 0;
    DECLARE @MaxCameraId INT;

    -- Get max ID for potential batch processing
    SELECT @MaxCameraId = ISNULL(MAX(CameraRelationId), 0) FROM dbo.Camera WHERE CameraName IS NOT NULL;

    IF @MaxCameraId > @BatchSize
    BEGIN
        PRINT 'Large dataset detected (' + CAST(@MaxCameraId AS VARCHAR) + ' cameras). Using batch processing...';
        
        IF @DryRun = 1
        BEGIN
            PRINT 'DRY RUN: Would anonymize camera names in batches for ' + CAST(@MaxCameraId AS VARCHAR) + ' records';
            PRINT 'DRY RUN: Camera names would be changed to Camera_001, Camera_002, etc.';
            SET @CameraBatchCount = (SELECT COUNT(*) FROM dbo.Camera WHERE CameraName IS NOT NULL);
        END
        ELSE
        BEGIN
            DECLARE @CurrentBatch INT = 0;
            DECLARE @ProcessedInBatch INT = 0;
            
            WHILE @CurrentBatch < @MaxCameraId
            BEGIN
                WITH NumberedCameras AS (
                    SELECT CameraRelationId, CameraName,
                           dbo.fn_GetAnonymizedSequence(CameraRelationId, @AnonymizationSeed, 'CAMERA') AS AnonymizedSeq
                    FROM dbo.Camera
                    WHERE CameraName IS NOT NULL
                    AND CameraRelationId > @CurrentBatch
                    AND CameraRelationId <= @CurrentBatch + @BatchSize
                )
                UPDATE dbo.Camera
                SET CameraName = 'Camera_' + RIGHT('000000' + CAST(NumberedCameras.AnonymizedSeq AS VARCHAR), 6)
                FROM dbo.Camera
                INNER JOIN NumberedCameras ON dbo.Camera.CameraRelationId = NumberedCameras.CameraRelationId;
                
                SET @ProcessedInBatch = @@ROWCOUNT;
                SET @CameraBatchCount = @CameraBatchCount + @ProcessedInBatch;
                SET @CurrentBatch = @CurrentBatch + @BatchSize;
                
                -- Progress reporting
                IF @CameraBatchCount % 25000 = 0
                    PRINT 'Progress: ' + CAST(@CameraBatchCount AS VARCHAR) + ' camera names processed';
            END
        END
    END
    ELSE
    BEGIN
        -- Standard processing for smaller datasets
        IF @DryRun = 1
        BEGIN
            PRINT 'DRY RUN: Would anonymize camera names for ' + 
                CAST((SELECT COUNT(*) FROM dbo.Camera WHERE CameraName IS NOT NULL) AS VARCHAR) + ' records';
            PRINT 'DRY RUN: Camera names would be changed to Camera_001, Camera_002, etc.';
            SET @CameraBatchCount = (SELECT COUNT(*) FROM dbo.Camera WHERE CameraName IS NOT NULL);
        END
        ELSE
        BEGIN
            WITH NumberedCameras AS (
                SELECT CameraRelationId, CameraName,
                       dbo.fn_GetAnonymizedSequence(CameraRelationId, @AnonymizationSeed, 'CAMERA') AS AnonymizedSeq
                FROM dbo.Camera
                WHERE CameraName IS NOT NULL
            )
            UPDATE dbo.Camera
            SET CameraName = 'Camera_' + RIGHT('000000' + CAST(NumberedCameras.AnonymizedSeq AS VARCHAR), 6)
            FROM dbo.Camera
            INNER JOIN NumberedCameras ON dbo.Camera.CameraRelationId = NumberedCameras.CameraRelationId;
            
            SET @CameraBatchCount = @@ROWCOUNT;
        END
    END

    SET @RowsAffected = @CameraBatchCount;
    SET @TotalRowsProcessed = @TotalRowsProcessed + @RowsAffected;

    -- Buffer operation for aggregated logging
    INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
    VALUES ('Camera Data', 'Camera', 'CameraName', @RowsAffected, 'Sequential Naming', @OperationStart, GETDATE(),
            'Renamed to Camera_001, Camera_002, etc. using seed ' + CAST(@AnonymizationSeed AS VARCHAR));

    PRINT 'Camera names anonymized. Rows affected: ' + CAST(@RowsAffected AS VARCHAR);

    -- =====================================================================================
    -- 2. ANONYMIZE CAMERA NOTES
    -- =====================================================================================
    PRINT 'Anonymizing camera notes...';
    SET @OperationStart = GETDATE();

    IF @DryRun = 1
    BEGIN
        PRINT 'DRY RUN: Would anonymize camera notes for ' + 
            CAST((SELECT COUNT(*) FROM dbo.Camera WHERE Note IS NOT NULL AND Note != '') AS VARCHAR) + ' records';
        PRINT 'DRY RUN: Notes would be set to "Generic camera location description"';
        SET @RowsAffected = (SELECT COUNT(*) FROM dbo.Camera WHERE Note IS NOT NULL AND Note != '');
    END
    ELSE
    BEGIN
        UPDATE dbo.Camera
        SET Note = 'Generic camera location description'
        WHERE Note IS NOT NULL AND Note != '';
        
        SET @RowsAffected = @@ROWCOUNT;
    END

    SET @TotalRowsProcessed = @TotalRowsProcessed + @RowsAffected;

    -- Buffer operation for aggregated logging
    INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
    VALUES ('Camera Data', 'Camera', 'Note', @RowsAffected, 'Generic Replacement', @OperationStart, GETDATE(),
            'Replaced with generic description');

    PRINT 'Camera notes anonymized. Rows affected: ' + CAST(@RowsAffected AS VARCHAR);

    -- =====================================================================================
    -- 3. ANONYMIZE GPS COORDINATES
    -- =====================================================================================
    PRINT 'Anonymizing GPS coordinates...';
    SET @OperationStart = GETDATE();

    IF @DryRun = 1
    BEGIN
        PRINT 'DRY RUN: Would anonymize GPS coordinates for ' + 
            CAST((SELECT COUNT(*) FROM dbo.Camera WHERE Latitude IS NOT NULL AND Longitude IS NOT NULL) AS VARCHAR) + ' records';
        PRINT 'DRY RUN: Coordinates would be randomized within bounds (' + 
            CAST(@GPSMinLatitude AS VARCHAR) + '-' + CAST(@GPSMaxLatitude AS VARCHAR) + 'N, ' + 
            CAST(@GPSMinLongitude AS VARCHAR) + '-' + CAST(@GPSMaxLongitude AS VARCHAR) + 'W)';
        SET @RowsAffected = (SELECT COUNT(*) FROM dbo.Camera WHERE Latitude IS NOT NULL AND Longitude IS NOT NULL);
    END
    ELSE
    BEGIN
        UPDATE dbo.Camera
        SET Latitude = ROUND(@GPSMinLatitude + 
            (dbo.fn_AnonymizationHash(CameraRelationId, @AnonymizationSeed, 'LAT') % CAST((@GPSMaxLatitude - @GPSMinLatitude) * 10000 AS INT)) / 10000.0, 4),
            Longitude = ROUND(@GPSMinLongitude + 
            (dbo.fn_AnonymizationHash(CameraRelationId, @AnonymizationSeed, 'LON') % CAST((@GPSMaxLongitude - @GPSMinLongitude) * 10000 AS INT)) / 10000.0, 4)
        WHERE Latitude IS NOT NULL AND Longitude IS NOT NULL;
        
        SET @RowsAffected = @@ROWCOUNT;
    END

    SET @TotalRowsProcessed = @TotalRowsProcessed + @RowsAffected;

    -- Buffer operation for aggregated logging
    INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
    VALUES ('Camera Data', 'Camera', 'Latitude,Longitude', @RowsAffected, 'GPS Randomization', @OperationStart, GETDATE(),
            'Randomized within bounds (' + CAST(@GPSMinLatitude AS VARCHAR) + ',' + CAST(@GPSMinLongitude AS VARCHAR) + ') to (' + 
            CAST(@GPSMaxLatitude AS VARCHAR) + ',' + CAST(@GPSMaxLongitude AS VARCHAR) + ')');

    PRINT 'GPS coordinates anonymized. Rows affected: ' + CAST(@RowsAffected AS VARCHAR);

    -- =====================================================================================
    -- 4. ANONYMIZE CAMERA TIMEZONES
    -- =====================================================================================
    PRINT 'Anonymizing camera timezones...';
    SET @OperationStart = GETDATE();

    IF @DryRun = 1
    BEGIN
        PRINT CONCAT('DRY RUN: Would anonymize timezones for ', 
            (SELECT COUNT(*) FROM dbo.Camera WHERE CameraTimeZone IS NOT NULL), ' records');
        PRINT 'DRY RUN: All timezones would be set to UTC';
        SET @RowsAffected = (SELECT COUNT(*) FROM dbo.Camera WHERE CameraTimeZone IS NOT NULL);
    END
    ELSE
    BEGIN
        UPDATE dbo.Camera 
        SET CameraTimeZone = 'UTC'
        WHERE CameraTimeZone IS NOT NULL;
        SET @RowsAffected = @@ROWCOUNT;
    END

    SET @TotalRowsProcessed = @TotalRowsProcessed + @RowsAffected;

    -- Buffer operation for aggregated logging
    INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
    VALUES ('Camera Data', 'Camera', 'CameraTimeZone', @RowsAffected, 'Timezone Standardization', @OperationStart, GETDATE(),
            'Standardized to UTC');

    PRINT 'Camera timezones anonymized. Rows affected: ' + CAST(@RowsAffected AS VARCHAR);

    -- =====================================================================================
    -- 5. ANONYMIZE ONVIF IMAGE URLS
    -- =====================================================================================
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'VideoDevices')
    BEGIN
        PRINT 'Anonymizing ONVIF Image URLs...';
        SET @OperationStart = GETDATE();
        
        IF @DryRun = 1
        BEGIN
            PRINT 'DRY RUN: Would anonymize ONVIF Image URLs for ' + 
                CAST((SELECT COUNT(*) FROM dbo.VideoDevices WHERE OnvifImageUrl IS NOT NULL) AS VARCHAR) + ' records';
            PRINT 'DRY RUN: URLs would use IP range ' + @CVE_IPRange_Cameras + '.x.x';
            SET @RowsAffected = (SELECT COUNT(*) FROM dbo.VideoDevices WHERE OnvifImageUrl IS NOT NULL);
        END
        ELSE
        BEGIN
            WITH NumberedVideoDevices AS (
                SELECT 
                    VideoDeviceId,
                    OnvifImageUrl,
                    ROW_NUMBER() OVER (ORDER BY VideoDeviceId) AS RowNum
                FROM dbo.VideoDevices
                WHERE OnvifImageUrl IS NOT NULL
            )
            UPDATE dbo.VideoDevices
            SET OnvifImageUrl = 'http://' + @CVE_IPRange_Cameras + '.' + 
                CAST((NumberedVideoDevices.RowNum - 1) / 254 + 1 AS VARCHAR) + '.' + 
                CAST((NumberedVideoDevices.RowNum - 1) % 254 + 1 AS VARCHAR) + 
                '/onvif/device_service'
            FROM dbo.VideoDevices
            INNER JOIN NumberedVideoDevices ON dbo.VideoDevices.VideoDeviceId = NumberedVideoDevices.VideoDeviceId;
            
            SET @RowsAffected = @@ROWCOUNT;
        END
        
        SET @TotalRowsProcessed = @TotalRowsProcessed + @RowsAffected;
        
        -- Buffer operation for aggregated logging
        INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
        VALUES ('Camera Data', 'VideoDevices', 'OnvifImageUrl', @RowsAffected, 'Sequential IP Assignment', @OperationStart, GETDATE(),
                'Generated sequential IPs in ' + @CVE_IPRange_Cameras + '.x.x range');
        
        PRINT 'ONVIF Image URLs anonymized. Rows affected: ' + CAST(@RowsAffected AS VARCHAR);
    END
    ELSE
    BEGIN
        PRINT 'VideoDevices table not found - skipping ONVIF URL anonymization';
    END

    -- =====================================================================================
    -- CREATE POST-ANONYMIZATION SNAPSHOT FOR VERIFICATION
    -- =====================================================================================
    PRINT '';
    PRINT 'Creating post-anonymization snapshot for verification...';

    IF OBJECT_ID('dbo.sp_CreatePostAnonymizationSnapshot') IS NOT NULL
    BEGIN
        EXEC dbo.sp_CreatePostAnonymizationSnapshot 
            @ExecutionID = @ExecutionID,
            @ModuleName = @ModuleName;
    END
    ELSE
    BEGIN
        PRINT 'Snapshot procedures not available. Post-anonymization snapshot skipped.';
    END

    -- =====================================================================================
    -- MODULE COMPLETION
    -- =====================================================================================
    DECLARE @ModuleEndTime DATETIME = GETDATE();
    DECLARE @ModuleExecutionTime INT = DATEDIFF(MILLISECOND, @ModuleStartTime, @ModuleEndTime);

    -- Flush aggregated logging buffer to reduce transaction log bloat
    IF OBJECT_ID('dbo.sp_FlushAnonymizationBuffer') IS NOT NULL
    BEGIN
        EXEC dbo.sp_FlushAnonymizationBuffer @DryRun = @DryRun;
    END

    -- Log module completion
    INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, ColumnName, RowsAffected, AnonymizationType, ExecutionTime, Notes)
    VALUES ('Camera Data', 'MODULE_COMPLETE', 'ALL', @TotalRowsProcessed, 'Module Execution', 
            @ModuleEndTime, 
            'Camera data anonymization completed in ' + CAST(DATEDIFF(SECOND, @ModuleStartTime, @ModuleEndTime) AS VARCHAR) + ' seconds');

    PRINT '';
    PRINT '=======================================================================';
    PRINT 'CAMERA DATA ANONYMIZATION MODULE COMPLETED';
    PRINT '=======================================================================';
    PRINT 'Total execution time: ' + CAST(DATEDIFF(SECOND, @ModuleStartTime, @ModuleEndTime) AS VARCHAR) + ' seconds';
    PRINT 'Total rows processed: ' + CAST(@TotalRowsProcessed AS VARCHAR);
    PRINT 'Operations completed: Camera names, notes, GPS coordinates, timezones, ONVIF URLs';
    PRINT 'Dry run mode: ' + CASE WHEN @DryRun = 1 THEN 'YES (No data modified)' ELSE 'NO (Data modified)' END;
    PRINT '=======================================================================';

    IF @DryRun = 1 OR @CommitChanges = 0 
        ROLLBACK TRAN 
    ELSE 
        COMMIT TRAN;

END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0 ROLLBACK TRAN;
    
    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();
    
    PRINT 'ERROR in Camera Data Anonymization: ' + @ErrorMessage;
    
    -- Log the error if possible
    IF OBJECT_ID('dbo.AnonymizationLog') IS NOT NULL
    BEGIN
        INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, ColumnName, RowsAffected, AnonymizationType, Notes)
        VALUES ('Camera Data', 'ERROR', 'MODULE_FAILURE', 0, 'Error', @ErrorMessage);
    END
    
    RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
END CATCH