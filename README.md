# CompleteView VMS v6 Database Anonymization

## OVERVIEW

This collection contains 12 standalone SQL modules for anonymizing CompleteView VMS database backups. Each module targets specific data types and can be executed independently in SQL Server Management Studio. The system provides comprehensive anonymization with advanced verification, performance optimization, and compliance reporting capabilities.

## EXECUTION ORDER (Execute in this exact sequence)

### STEP 1: Setup and Validation (REQUIRED FIRST)

- **File:** `01_Setup_and_Validation.sql`
- **Purpose:** Creates anonymization infrastructure, validates database state, establishes logging and snapshot systems
- **Features:** Configuration management, pre/post snapshot procedures, hash verification, buffer logging

### STEP 2: Camera Data Anonymization

- **File:** `02_Camera_Data_Anonymization.sql`
- **Purpose:** Anonymizes camera names, IP addresses, GPS coordinates, ONVIF URLs, location descriptions
- **Features:** Sequential naming (Camera_001, Camera_002), deterministic GPS randomization, ONVIF credential redaction

### STEP 3: Server Data Anonymization

- **File:** `03_Server_Data_Anonymization.sql`
- **Purpose:** Anonymizes server hostnames, IP addresses, DNS entries, database connections, credentials
- **Features:** Sequential server naming (SRV-000001), IP range assignment, password strategy options, connection string redaction

### STEP 4: User Account Anonymization

- **File:** `04_User_Account_Anonymization.sql`
- **Purpose:** Anonymizes usernames, passwords, personal information, email addresses, full names
- **Features:** Sequential user naming (User_000001), configurable password strategies, email domain standardization

### STEP 5: Network Device Anonymization

- **File:** `05_Network_Device_Anonymization.sql`
- **Purpose:** Anonymizes video devices, alarm devices, audio devices, MAC addresses, serial numbers, firmware
- **Features:** Deterministic MAC generation, manufacturer anonymization, model/serial redaction, IP range assignment

### STEP 6: Integration Credentials Anonymization

- **File:** `06_Integration_Credentials_Anonymization.sql`
- **Purpose:** Anonymizes API keys, certificates, authentication tokens, connection strings, integration endpoints
- **Features:** Credential redaction, hostname anonymization, certificate replacement, token sanitization

### STEP 7: URL and File Path Anonymization

- **File:** `07_URL_and_FilePath_Anonymization.sql`
- **Purpose:** Anonymizes file paths, UNC paths, volume settings, stream profiles, recording paths
- **Features:** Path structure preservation, UNC path anonymization, stream URL redaction, volume path standardization

### STEP 8: Email and Domain Anonymization

- **File:** `08_Email_and_Domain_Anonymization.sql`
- **Purpose:** Anonymizes email addresses, domain names, SMTP configurations, notification settings
- **Features:** Domain standardization (@example.com), SMTP credential redaction, email message anonymization

### STEP 9: Verification and Reporting

- **File:** `09_Verification_and_Reporting.sql`
- **Purpose:** Validates anonymization results, performs pattern detection, generates compliance reports
- **Features:** Snapshot comparison, hash verification, pattern analysis, risk assessment, detailed reporting

### STEP 10: Data Masking Verification

- **File:** `10_Data_Masking_Verification.sql`
- **Purpose:** Advanced security verification with deep pattern analysis and data leakage detection
- **Features:** Email pattern detection, IP address scanning, domain name analysis, phone number detection, security scoring

### STEP 11: Performance Optimization

- **File:** `11_Performance_Optimization.sql`
- **Purpose:** Comprehensive database maintenance including index rebuilding, statistics updates, fragmentation analysis
- **Features:** Automated index analysis, statistics refresh, fragmentation reporting, performance recommendations

### STEP 12: Final Verification

- **File:** `12_Final_Verification.sql`
- **Purpose:** Final comprehensive verification with pattern-based analysis and statistical validation
- **Features:** Pattern-based verification, statistical analysis, threshold checking, compliance validation, final reporting

## ADDITIONAL UTILITY FILES

### Performance Enhancement (Optional)

- **CONFIG_MANAGEMENT.sql** - Performance-optimized configuration system with strongly-typed columns, modern accessor functions, and centralized constants. Provides significant performance benefits over the default configuration structure created by Module 01.
  - **Features:** Auto-detects legacy vs modern table structures, seamless migration, configuration validation, bulk operations, transaction safety
  - **Benefits:** Faster configuration access, better type safety, centralized magic constants, improved maintainability

### Advanced Verification (Optional)

- **VERIFICATION_UTILITY.sql** - Advanced verification tools for regulatory audits, security assessments, and specialized compliance reporting. Only needed for advanced compliance scenarios beyond standard verification provided by Modules 10 and 12.
  - **Features:** Deep pattern analysis (`sp_DeepPatternAnalysis`), statistical quality assessment (`sp_StatisticalAnonymizationQuality`), compliance reporting (`sp_ComplianceReport`), string pattern analysis (`fn_AnalyzeStringPattern`)
  - **Use Cases:** Regulatory audits, security assessments, troubleshooting verification failures, export requirements for external stakeholders

## EXECUTION RECOMMENDATIONS

### Minimum Required Execution (Complete Anonymization)

```sql
:r "01_Setup_and_Validation.sql"
:r "02_Camera_Data_Anonymization.sql"
:r "03_Server_Data_Anonymization.sql"
:r "04_User_Account_Anonymization.sql"
:r "05_Network_Device_Anonymization.sql" 
:r "06_Integration_Credentials_Anonymization.sql"
:r "07_URL_and_FilePath_Anonymization.sql"
:r "08_Email_and_Domain_Anonymization.sql"
:r "09_Verification_and_Reporting.sql"
:r "10_Data_Masking_Verification.sql"
:r "11_Performance_Optimization.sql"
:r "12_Final_Verification.sql"
```

### Recommended Execution (With Performance Optimization)

```sql
:r "01_Setup_and_Validation.sql"
:r "CONFIG_MANAGEMENT.sql"  -- Optional: Performance upgrade
:r "02_Camera_Data_Anonymization.sql"
:r "03_Server_Data_Anonymization.sql"
:r "04_User_Account_Anonymization.sql"
:r "05_Network_Device_Anonymization.sql" 
:r "06_Integration_Credentials_Anonymization.sql"
:r "07_URL_and_FilePath_Anonymization.sql"
:r "08_Email_and_Domain_Anonymization.sql"
:r "09_Verification_and_Reporting.sql"
:r "10_Data_Masking_Verification.sql"
:r "11_Performance_Optimization.sql"
:r "12_Final_Verification.sql"
```

### Advanced Compliance Execution (Full Suite)

```sql
:r "01_Setup_and_Validation.sql"
:r "CONFIG_MANAGEMENT.sql"  -- Optional: Performance upgrade
:r "02_Camera_Data_Anonymization.sql"
:r "03_Server_Data_Anonymization.sql"
:r "04_User_Account_Anonymization.sql"
:r "05_Network_Device_Anonymization.sql" 
:r "06_Integration_Credentials_Anonymization.sql"
:r "07_URL_and_FilePath_Anonymization.sql"
:r "08_Email_and_Domain_Anonymization.sql"
:r "09_Verification_and_Reporting.sql"
:r "10_Data_Masking_Verification.sql"
:r "11_Performance_Optimization.sql"
:r "12_Final_Verification.sql"
:r "VERIFICATION_UTILITY.sql"  -- Optional: Advanced compliance tools
```

## EXECUTION INSTRUCTIONS

1. **Open SQL Server Management Studio (SSMS)**
2. **Connect to your CompleteView database**
3. **Execute modules in numerical order (01-12)**
4. **Monitor execution progress** in Messages tab
5. **Check final verification results** from Module 12

### Batch Execution (Alternative Method)

Create a master script with the commands above, or execute each file individually through SSMS.

## CONFIGURATION

- Configuration managed through `AnonymizationConfig` table (created in Module 01)
- Use `CONFIG_MANAGEMENT.sql` for performance-optimized configuration structure
- All modules use centralized configuration via `dbo.fn_GetConfigValue()` function

## TROUBLESHOOTING

### Common Issues

- **"Table does not exist"**: Ensure Module 01 ran properly and created required infrastructure
- **"Permission denied"**: Requires `db_owner` role or equivalent permissions for all operations
- **"Configuration not found"**: Run Module 01 first to create `AnonymizationConfig` table
- **"Verification failed"**: Run Module 12 for detailed analysis, use `VERIFICATION_UTILITY.sql` for deep analysis
- **"Timeout errors"**: Increase SSMS timeout (Tools > Options > Query Execution > SQL Server > General)
- **"Transaction log full"**: Ensure adequate transaction log space for rollback operations
- **"Index rebuild failed"**: Check disk space and permissions for Module 11 operations

### Verification Results and Actions

#### Module 09 & 10 Results

- **PASSED**: All standard checks passed, proceed to performance optimization
- **PASSED_WITH_WARNINGS**: Minor issues detected, review detailed findings before proceeding
- **FAILED_MINOR**: Some data patterns detected, re-run specific modules or investigate patterns
- **FAILED_MAJOR**: Significant data leakage detected, investigate thoroughly before use

#### Module 12 Final Verification

- **Pattern-Based Verification**: Checks for remaining real data patterns using statistical analysis
- **Threshold Analysis**: Validates anonymization effectiveness against configurable thresholds
- **Compliance Validation**: Ensures all required anonymization standards are met

#### Advanced Troubleshooting

- **Use `sp_DeepPatternAnalysis`**: For detailed pattern analysis when standard verification shows issues
- **Use `sp_StatisticalAnonymizationQuality`**: For quality assessment and effectiveness measurement
- **Use `sp_ComplianceReport`**: For detailed compliance reporting and audit trail generation
- **Check `AnonymizationLog` table**: For detailed execution history and error tracking

### Performance Troubleshooting

- **Slow execution**: Consider using `CONFIG_MANAGEMENT.sql` for performance optimization
- **High fragmentation**: Module 11 provides detailed fragmentation analysis and recommendations
- **Memory issues**: Adjust batch sizes in configuration for large datasets
- **Lock timeouts**: Ensure no active connections during anonymization process

## File Descriptions

| File | Purpose | Key Features | Estimated Time |
|------|---------|--------------|----------------|
| `01_Setup_and_Validation.sql` | Infrastructure setup, database validation | Configuration tables, logging system, snapshot procedures, hash verification | 1-2 minutes |
| `02_Camera_Data_Anonymization.sql` | Camera data anonymization | Sequential naming, GPS randomization, ONVIF credential redaction | 2-5 minutes |
| `03_Server_Data_Anonymization.sql` | Server and network infrastructure | Sequential naming, IP range assignment, credential strategies | 2-5 minutes |
| `04_User_Account_Anonymization.sql` | User accounts and personal data | Sequential user naming, password strategies, email standardization | 1-3 minutes |
| `05_Network_Device_Anonymization.sql` | Network devices and hardware | MAC generation, manufacturer anonymization, serial redaction | 3-8 minutes |
| `06_Integration_Credentials_Anonymization.sql` | API keys and integration data | Credential redaction, hostname anonymization, token sanitization | 1-2 minutes |
| `07_URL_and_FilePath_Anonymization.sql` | File paths and URLs | Path structure preservation, UNC anonymization, stream redaction | 2-4 minutes |
| `08_Email_and_Domain_Anonymization.sql` | Email and domain data | Domain standardization, SMTP redaction, message anonymization | 1-3 minutes |
| `09_Verification_and_Reporting.sql` | Anonymization validation | Snapshot comparison, pattern analysis, risk assessment | 2-3 minutes |
| `10_Data_Masking_Verification.sql` | Advanced security verification | Deep pattern detection, data leakage analysis, security scoring | 3-5 minutes |
| `11_Performance_Optimization.sql` | Database maintenance | Index rebuilding, statistics updates, fragmentation analysis | 5-15 minutes |
| `12_Final_Verification.sql` | Final comprehensive verification | Pattern-based analysis, statistical validation, compliance reporting | 2-5 minutes |
| `CONFIG_MANAGEMENT.sql` | Performance-optimized configuration | Strongly-typed columns, modern accessors, centralized constants | 2-3 minutes |
| `VERIFICATION_UTILITY.sql` | Advanced compliance tools | Deep pattern analysis, statistical quality assessment, compliance reporting | 2-3 minutes |

## PERFORMANCE OPTIMIZATIONS

The anonymization system has been optimized for performance and scalability:

### Core Performance Features

- **Set-Based Operations**: All modules use CTEs and batch operations instead of cursors for maximum performance
- **Deterministic Hashing**: Consistent anonymization using `fn_AnonymizationHash` and `fn_GetAnonymizedSequence` functions
- **Batch Processing**: Configurable batch sizes for large datasets
- **Transaction Management**: Proper transaction handling with rollback capabilities

### Configuration Performance

- **Centralized Configuration**: All settings managed through `AnonymizationConfig` table
- **Optional Typed Configuration**: `CONFIG_MANAGEMENT.sql` provides strongly-typed columns for better performance
- **Configuration Caching**: `fn_GetConfigValue` function provides efficient configuration access
- **Magic Constants**: All hardcoded values moved to centralized configuration

### Database Optimization

- **Index Analysis**: Module 11 performs comprehensive fragmentation analysis
- **Statistics Updates**: Automatic statistics refresh for optimal query performance
- **Maintenance Recommendations**: Detailed performance recommendations based on database state
- **Resource Monitoring**: Tracks execution times and resource usage

### Verification Performance

- **Snapshot-Based Verification**: Efficient pre/post comparison using hash-based detection
- **Pattern-Based Analysis**: Optimized pattern detection with configurable sample sizes
- **Risk-Based Reporting**: Prioritized verification focusing on high-risk data patterns

## REQUIREMENTS

### System Requirements

- **SQL Server 2016+** (SQL Server 2012+ for basic functionality)
- **Database Permissions:** `db_owner` role or equivalent permissions
- **SQL Server Management Studio 17.0+** (or any SQL Server client)
- **Adequate disk space:** 2x database size recommended for operations

### Database Requirements

- **CompleteView VMS database** (validates for Camera, User, Server, VideoDevices tables)
- **Backup recommended** before execution (configurable requirement)
- **Transaction log space** for rollback capabilities
- **Statistics and index maintenance** permissions

### Performance Considerations

- **Memory:** Sufficient RAM for index rebuilding operations
- **CPU:** Multi-core recommended for parallel operations
- **I/O:** Fast storage recommended for large databases
- **Timeout settings:** Increase SSMS timeout for large operations

## FEATURES AND CAPABILITIES

### Security Features

- **Deterministic Anonymization:** Consistent results across multiple runs using seeded randomization
- **Comprehensive Coverage:** Handles all major data types (PII, network, credentials, paths, emails)
- **Verification Systems:** Multiple verification layers including pattern detection and hash comparison
- **Risk Assessment:** Automated risk scoring and compliance reporting

### Operational Features

- **Dry Run Mode:** Preview changes before execution across all modules
- **Transaction Safety:** Full rollback capabilities with proper transaction management
- **Comprehensive Logging:** Detailed audit trail with execution tracking and error handling
- **Modular Design:** Execute individual modules or complete suite as needed

### Advanced Capabilities

- **Pattern-Based Verification:** Advanced pattern detection for data leakage prevention
- **Statistical Analysis:** Quality assessment and anonymization effectiveness measurement
- **Performance Optimization:** Automated database maintenance and optimization
- **Compliance Reporting:** Structured reports for regulatory requirements

## NOTES

- **Designed for database backups** - not live production systems
- **All anonymization occurs locally** - no external data transmission
- **Full audit trail** maintained in AnonymizationLog table with execution tracking
- **Each file can be executed independently** with proper dependency validation
- **Configurable anonymization strategies** through centralized configuration system
- **Supports both legacy and modern configuration structures** for flexibility
