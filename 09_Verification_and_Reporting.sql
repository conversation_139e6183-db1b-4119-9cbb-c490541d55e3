-- =====================================================================================
-- COMPLETEVIEW DATABASE ANONYMIZATION - STANDALONE MODULE
-- File: 09_Verification_and_Reporting.sql

-- =====================================================================================
-- This is a standalone, pre-configured SQL module that can be executed directly
-- in SQL Server Management Studio without requiring PowerShell configuration.
-- 
-- IMPORTANT: 
-- 1. BACKUP YOUR DATABASE BEFORE RUNNING THIS SCRIPT
-- 2. Review the configuration values below before execution
-- 3. Execute modules in numerical order (01, 02, 03, etc.)
-- =====================================================================================

SET NOCOUNT ON;
SET XACT_ABORT ON;

-- =====================================================================================
-- CENTRALIZED CONFIGURATION LOADER
-- =====================================================================================

-- Verify configuration prerequisites
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationConfig')
BEGIN
    THROW 50001, 'AnonymizationConfig table not found. Please run Module 01 (Setup and Validation) first.', 1;
END

IF OBJECT_ID('dbo.fn_GetConfigValue') IS NULL
BEGIN
    THROW 50002, 'Configuration helper function fn_GetConfigValue not found. Please run Module 01 (Setup and Validation) first.', 1;
END

-- Load configuration values from centralized table
DECLARE @ExecutionID VARCHAR(50) = 'MODULE09_' + REPLACE(CAST(NEWID() AS VARCHAR(36)), '-', '');
DECLARE @ModuleName VARCHAR(100) = 'Verification and Reporting';

PRINT 'Starting ' + @ModuleName + '...';
PRINT 'Execution ID: ' + @ExecutionID;

DECLARE @AnonymizationSeed INT = CAST(dbo.fn_GetConfigValue('AnonymizationSeed') AS INT);
DECLARE @AnonymizedDomain NVARCHAR(100) = dbo.fn_GetConfigValue('AnonymizedDomain');
DECLARE @EmailSuffix NVARCHAR(100) = dbo.fn_GetConfigValue('EmailSuffix');
DECLARE @DefaultPassword NVARCHAR(100) = dbo.fn_GetConfigValue('DefaultPassword');
DECLARE @BatchSize INT = CAST(dbo.fn_GetConfigValue('BatchSize') AS INT);
DECLARE @DryRun BIT = CAST(dbo.fn_GetConfigValue('DryRun') AS BIT);
DECLARE @CommitChanges BIT = CAST(dbo.fn_GetConfigValue('CommitChanges') AS BIT);
DECLARE @UsePerformanceOptimizations BIT = CAST(dbo.fn_GetConfigValue('UsePerformanceOptimizations') AS BIT);
DECLARE @RequireRecentBackup BIT = CAST(dbo.fn_GetConfigValue('RequireRecentBackup') AS BIT);
DECLARE @BackupMaxAgeDays INT = CAST(dbo.fn_GetConfigValue('BackupMaxAgeDays') AS INT);

-- Initialize module variables
DECLARE @TotalRowsProcessed INT = 0;
DECLARE @ModuleStartTime DATETIME = GETDATE();
DECLARE @OperationStart DATETIME;
DECLARE @RowsAffected INT;

PRINT 'Configuration loaded from centralized table for Verification and Reporting';

-- =====================================================================================
-- MODULE 09: VERIFICATION AND REPORTING
-- =====================================================================================
-- Description: Validates anonymization results and generates compliance reports
-- Security Level: CRITICAL - Final verification of data anonymization
-- =====================================================================================

PRINT '=======================================================================';
PRINT 'MODULE 09: VERIFICATION AND REPORTING';
PRINT '=======================================================================';
PRINT 'Execution Mode: ' + CASE WHEN @DryRun = 1 THEN 'DRY RUN (Preview Only)' ELSE 'LIVE EXECUTION' END;
PRINT '';

BEGIN TRY
    BEGIN TRAN;

    -- =====================================================================================
    -- ANONYMIZATION LOG ANALYSIS
    -- =====================================================================================
    PRINT 'Analyzing anonymization execution logs...';
    SET @OperationStart = GETDATE();

    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationLog')
    BEGIN
        -- Summary of all anonymization operations
        PRINT '';
        PRINT 'ANONYMIZATION EXECUTION SUMMARY:';
        PRINT '================================';
        
        SELECT 
            ModuleName,
            COUNT(*) AS Operations,
            SUM(RowsAffected) AS TotalRowsProcessed,
            AVG(ExecutionTimeMs) AS AvgExecutionTimeMs,
            MIN(LogDate) AS FirstOperation,
            MAX(LogDate) AS LastOperation
        FROM dbo.AnonymizationLog
        WHERE ModuleName != 'Verification and Reporting'
        GROUP BY ModuleName
        ORDER BY MIN(LogDate);

        -- Detailed operation breakdown
        PRINT '';
        PRINT 'DETAILED OPERATION BREAKDOWN:';
        PRINT '=============================';
        
        SELECT TOP 20
            ModuleName,
            TableName,
            ColumnName,
            RowsAffected,
            AnonymizationType,
            LogDate,
            Notes
        FROM dbo.AnonymizationLog
        WHERE ModuleName != 'Verification and Reporting'
        ORDER BY LogDate DESC;

        -- Check for any errors in the log
        DECLARE @ErrorCount INT = (
            SELECT COUNT(*) 
            FROM dbo.AnonymizationLog 
            WHERE AnonymizationType = 'Error' OR Notes LIKE '%ERROR%'
        );

        IF @ErrorCount > 0
        BEGIN
            PRINT '';
            PRINT 'WARNING: ' + CAST(@ErrorCount AS VARCHAR) + ' errors found in anonymization log:';
            SELECT ModuleName, TableName, Notes, LogDate
            FROM dbo.AnonymizationLog
            WHERE AnonymizationType = 'Error' OR Notes LIKE '%ERROR%'
            ORDER BY LogDate DESC;
        END
        ELSE
        BEGIN
            PRINT '';
            PRINT 'SUCCESS: No errors found in anonymization execution log.';
        END

        SET @TotalRowsProcessed = @TotalRowsProcessed + 1;
    END
    ELSE
    BEGIN
        PRINT 'WARNING: AnonymizationLog table not found. Cannot analyze execution history.';
    END

    -- =====================================================================================
    -- SNAPSHOT COMPARISON VERIFICATION
    -- =====================================================================================
    PRINT '';
    PRINT 'Performing snapshot comparison verification...';

    -- Check if snapshot tables exist and have data
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationPreSnapshot')
       AND EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationPostSnapshot')
    BEGIN
        -- Get the most recent execution ID from snapshots
        DECLARE @LatestExecutionID VARCHAR(50);
        SELECT TOP 1 @LatestExecutionID = ExecutionID 
        FROM dbo.AnonymizationPreSnapshot 
        ORDER BY SnapshotDate DESC;
        
        IF @LatestExecutionID IS NULL
        BEGIN
            PRINT 'No pre-anonymization snapshots found. Cannot perform comparison verification.';
            PRINT 'Snapshots should be created before anonymization begins.';
        END
        ELSE
        BEGIN
            PRINT 'Found snapshots for execution: ' + @LatestExecutionID;
            PRINT 'Performing pre/post comparison analysis...';
            PRINT '';
            
            -- Execute snapshot comparison
            IF OBJECT_ID('dbo.sp_CompareSnapshotsAndDetectMisses') IS NOT NULL
            BEGIN
                EXEC dbo.sp_CompareSnapshotsAndDetectMisses 
                    @ExecutionID = @LatestExecutionID,
                    @DryRun = @DryRun;
            END
            ELSE
            BEGIN
                PRINT 'Snapshot comparison procedure not found. Performing basic comparison...';
                
                -- Basic snapshot comparison
                DECLARE @PreSnapshotCount INT = (SELECT COUNT(*) FROM dbo.AnonymizationPreSnapshot WHERE ExecutionID = @LatestExecutionID);
                DECLARE @PostSnapshotCount INT = (SELECT COUNT(*) FROM dbo.AnonymizationPostSnapshot WHERE ExecutionID = @LatestExecutionID);
                
                PRINT 'Pre-anonymization snapshot records: ' + CAST(@PreSnapshotCount AS VARCHAR);
                PRINT 'Post-anonymization snapshot records: ' + CAST(@PostSnapshotCount AS VARCHAR);
                
                IF @PreSnapshotCount = @PostSnapshotCount
                    PRINT 'SUCCESS: Snapshot record counts match.';
                ELSE
                    PRINT 'WARNING: Snapshot record counts do not match. Data may have been added/removed during anonymization.';
            END
            
            PRINT '';
            PRINT 'Pre/post snapshot comparison completed.';
            
            -- Execute hash-based verification
            PRINT '';
            PRINT 'Performing hash-based verification to detect any surviving original values...';
            IF OBJECT_ID('dbo.sp_VerifyNoOriginalValuesRemain') IS NOT NULL
            BEGIN
                EXEC dbo.sp_VerifyNoOriginalValuesRemain 
                    @ExecutionID = @LatestExecutionID,
                    @DryRun = @DryRun;
            END
            ELSE
            BEGIN
                PRINT 'Hash-based verification procedure not found. Skipping original value detection.';
            END
        END
    END
    ELSE
    BEGIN
        PRINT 'WARNING: Snapshot tables not found. Pre/post comparison verification skipped.';
        PRINT 'To enable snapshot verification, ensure Module 01 creates snapshot infrastructure';
        PRINT 'and anonymization modules call snapshot procedures.';
        PRINT '';
    END

    -- =====================================================================================
    -- PATTERN-BASED VERIFICATION
    -- =====================================================================================
    PRINT '';
    PRINT 'Performing pattern-based verification across database...';
    SET @OperationStart = GETDATE();

    -- Create temporary table for verification results
    CREATE TABLE #VerificationResults (
        CheckType NVARCHAR(50),
        TableName NVARCHAR(128),
        ColumnName NVARCHAR(128),
        IssueCount INT,
        SampleValue NVARCHAR(500),
        Severity NVARCHAR(20)
    );

    -- Check for remaining email patterns
    PRINT 'Checking for remaining email patterns...';
    DECLARE @TableName NVARCHAR(128);
    DECLARE @ColumnName NVARCHAR(128);
    DECLARE @SQL NVARCHAR(MAX);
    DECLARE @IssueCount INT;

    DECLARE verification_cursor CURSOR FOR
    SELECT t.TABLE_NAME, c.COLUMN_NAME
    FROM INFORMATION_SCHEMA.TABLES t
    INNER JOIN INFORMATION_SCHEMA.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME
    WHERE t.TABLE_TYPE = 'BASE TABLE'
    AND c.DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext')
    AND t.TABLE_NAME NOT IN ('AnonymizationLog', 'AnonymizationLogBuffer', 'AnonymizationConfig', 
                             'AnonymizationPreSnapshot', 'AnonymizationPostSnapshot')
    ORDER BY t.TABLE_NAME, c.COLUMN_NAME;

    OPEN verification_cursor;
    FETCH NEXT FROM verification_cursor INTO @TableName, @ColumnName;

    WHILE @@FETCH_STATUS = 0
    BEGIN
        -- Check for email patterns
        SET @SQL = 'SELECT @Count = COUNT(*) FROM dbo.' + QUOTENAME(@TableName) + 
                   ' WHERE ' + QUOTENAME(@ColumnName) + ' LIKE ''%@%'' AND ' + 
                   QUOTENAME(@ColumnName) + ' NOT LIKE ''%' + @EmailSuffix + ''' AND ' +
                   QUOTENAME(@ColumnName) + ' NOT LIKE ''%@company.local''';
        
        EXEC sp_executesql @SQL, N'@Count INT OUTPUT', @Count = @IssueCount OUTPUT;
        
        IF @IssueCount > 0
        BEGIN
            DECLARE @SampleValue NVARCHAR(500);
            SET @SQL = 'SELECT TOP 1 @Sample = ' + QUOTENAME(@ColumnName) + 
                       ' FROM dbo.' + QUOTENAME(@TableName) + 
                       ' WHERE ' + QUOTENAME(@ColumnName) + ' LIKE ''%@%'' AND ' + 
                       QUOTENAME(@ColumnName) + ' NOT LIKE ''%' + @EmailSuffix + ''' AND ' +
                       QUOTENAME(@ColumnName) + ' NOT LIKE ''%@company.local''';
            
            EXEC sp_executesql @SQL, N'@Sample NVARCHAR(500) OUTPUT', @Sample = @SampleValue OUTPUT;
            
            INSERT INTO #VerificationResults (CheckType, TableName, ColumnName, IssueCount, SampleValue, Severity)
            VALUES ('Email Pattern', @TableName, @ColumnName, @IssueCount, @SampleValue, 'HIGH');
        END

        -- Check for IP address patterns
        SET @SQL = 'SELECT @Count = COUNT(*) FROM dbo.' + QUOTENAME(@TableName) + 
                   ' WHERE ' + QUOTENAME(@ColumnName) + ' LIKE ''%.%.%.%'' AND ' + 
                   QUOTENAME(@ColumnName) + ' NOT LIKE ''192.168.%'' AND ' +
                   QUOTENAME(@ColumnName) + ' NOT LIKE ''10.%'' AND ' +
                   QUOTENAME(@ColumnName) + ' NOT LIKE ''172.16.%''';
        
        EXEC sp_executesql @SQL, N'@Count INT OUTPUT', @Count = @IssueCount OUTPUT;
        
        IF @IssueCount > 0
        BEGIN
            SET @SQL = 'SELECT TOP 1 @Sample = ' + QUOTENAME(@ColumnName) + 
                       ' FROM dbo.' + QUOTENAME(@TableName) + 
                       ' WHERE ' + QUOTENAME(@ColumnName) + ' LIKE ''%.%.%.%'' AND ' + 
                       QUOTENAME(@ColumnName) + ' NOT LIKE ''192.168.%'' AND ' +
                       QUOTENAME(@ColumnName) + ' NOT LIKE ''10.%'' AND ' +
                       QUOTENAME(@ColumnName) + ' NOT LIKE ''172.16.%''';
            
            EXEC sp_executesql @SQL, N'@Sample NVARCHAR(500) OUTPUT', @Sample = @SampleValue OUTPUT;
            
            INSERT INTO #VerificationResults (CheckType, TableName, ColumnName, IssueCount, SampleValue, Severity)
            VALUES ('IP Address', @TableName, @ColumnName, @IssueCount, @SampleValue, 'MEDIUM');
        END

        -- Check for domain patterns
        SET @SQL = 'SELECT @Count = COUNT(*) FROM dbo.' + QUOTENAME(@TableName) + 
                   ' WHERE (' + QUOTENAME(@ColumnName) + ' LIKE ''%.com'' OR ' + 
                   QUOTENAME(@ColumnName) + ' LIKE ''%.org'' OR ' +
                   QUOTENAME(@ColumnName) + ' LIKE ''%.net'') AND ' +
                   QUOTENAME(@ColumnName) + ' NOT LIKE ''%example.com'' AND ' +
                   QUOTENAME(@ColumnName) + ' NOT LIKE ''%company.local''';
        
        EXEC sp_executesql @SQL, N'@Count INT OUTPUT', @Count = @IssueCount OUTPUT;
        
        IF @IssueCount > 0
        BEGIN
            SET @SQL = 'SELECT TOP 1 @Sample = ' + QUOTENAME(@ColumnName) + 
                       ' FROM dbo.' + QUOTENAME(@TableName) + 
                       ' WHERE (' + QUOTENAME(@ColumnName) + ' LIKE ''%.com'' OR ' + 
                       QUOTENAME(@ColumnName) + ' LIKE ''%.org'' OR ' +
                       QUOTENAME(@ColumnName) + ' LIKE ''%.net'') AND ' +
                       QUOTENAME(@ColumnName) + ' NOT LIKE ''%example.com'' AND ' +
                       QUOTENAME(@ColumnName) + ' NOT LIKE ''%company.local''';
            
            EXEC sp_executesql @SQL, N'@Sample NVARCHAR(500) OUTPUT', @Sample = @SampleValue OUTPUT;
            
            INSERT INTO #VerificationResults (CheckType, TableName, ColumnName, IssueCount, SampleValue, Severity)
            VALUES ('Domain Pattern', @TableName, @ColumnName, @IssueCount, @SampleValue, 'MEDIUM');
        END

        FETCH NEXT FROM verification_cursor INTO @TableName, @ColumnName;
    END

    CLOSE verification_cursor;
    DEALLOCATE verification_cursor;

    -- Report verification results
    DECLARE @TotalIssues INT = (SELECT COUNT(*) FROM #VerificationResults);
    DECLARE @HighSeverityIssues INT = (SELECT COUNT(*) FROM #VerificationResults WHERE Severity = 'HIGH');
    DECLARE @MediumSeverityIssues INT = (SELECT COUNT(*) FROM #VerificationResults WHERE Severity = 'MEDIUM');

    PRINT '';
    PRINT 'PATTERN-BASED VERIFICATION RESULTS:';
    PRINT '===================================';
    PRINT 'Total issues found: ' + CAST(@TotalIssues AS VARCHAR);
    PRINT 'High severity issues: ' + CAST(@HighSeverityIssues AS VARCHAR);
    PRINT 'Medium severity issues: ' + CAST(@MediumSeverityIssues AS VARCHAR);

    IF @TotalIssues > 0
    BEGIN
        PRINT '';
        PRINT 'DETAILED VERIFICATION FINDINGS:';
        SELECT CheckType, TableName, ColumnName, IssueCount, 
               LEFT(SampleValue, 50) + CASE WHEN LEN(SampleValue) > 50 THEN '...' ELSE '' END AS SampleValue,
               Severity
        FROM #VerificationResults
        ORDER BY Severity DESC, IssueCount DESC;
    END
    ELSE
    BEGIN
        PRINT '';
        PRINT 'SUCCESS: No pattern-based verification issues found.';
    END

    DROP TABLE #VerificationResults;
    SET @TotalRowsProcessed = @TotalRowsProcessed + @TotalIssues;

    -- =====================================================================================
    -- COMPLIANCE REPORTING
    -- =====================================================================================
    PRINT '';
    PRINT 'Generating compliance report...';
    SET @OperationStart = GETDATE();

    -- Database anonymization status
    PRINT '';
    PRINT 'DATABASE ANONYMIZATION STATUS REPORT:';
    PRINT '====================================';
    PRINT 'Database Name: ' + DB_NAME();
    PRINT 'Report Generated: ' + CONVERT(VARCHAR, GETDATE(), 120);
    PRINT 'Anonymization Seed: ' + CAST(@AnonymizationSeed AS VARCHAR);
    PRINT 'Anonymized Domain: ' + @AnonymizedDomain;
    PRINT 'Email Suffix: ' + @EmailSuffix;

    -- Table processing summary
    PRINT '';
    PRINT 'TABLE PROCESSING SUMMARY:';
    PRINT '========================';
    
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationLog')
    BEGIN
        SELECT DISTINCT
            TableName,
            COUNT(*) AS OperationsPerformed,
            SUM(RowsAffected) AS TotalRowsModified
        FROM dbo.AnonymizationLog
        WHERE TableName != 'MODULE_COMPLETE' AND TableName != 'ERROR'
        GROUP BY TableName
        ORDER BY SUM(RowsAffected) DESC;
    END

    -- Overall anonymization assessment
    DECLARE @OverallStatus NVARCHAR(50);
    DECLARE @RiskLevel NVARCHAR(20);

    IF @HighSeverityIssues > 0
    BEGIN
        SET @OverallStatus = 'FAILED - HIGH RISK DATA DETECTED';
        SET @RiskLevel = 'HIGH';
    END
    ELSE IF @MediumSeverityIssues > 5
    BEGIN
        SET @OverallStatus = 'PASSED WITH WARNINGS';
        SET @RiskLevel = 'MEDIUM';
    END
    ELSE IF @MediumSeverityIssues > 0
    BEGIN
        SET @OverallStatus = 'PASSED WITH MINOR ISSUES';
        SET @RiskLevel = 'LOW';
    END
    ELSE
    BEGIN
        SET @OverallStatus = 'PASSED - FULLY ANONYMIZED';
        SET @RiskLevel = 'MINIMAL';
    END

    PRINT '';
    PRINT 'OVERALL ANONYMIZATION ASSESSMENT:';
    PRINT '=================================';
    PRINT 'Status: ' + @OverallStatus;
    PRINT 'Risk Level: ' + @RiskLevel;
    PRINT 'High Severity Issues: ' + CAST(@HighSeverityIssues AS VARCHAR);
    PRINT 'Medium Severity Issues: ' + CAST(@MediumSeverityIssues AS VARCHAR);

    -- =====================================================================================
    -- MODULE COMPLETION
    -- =====================================================================================
    DECLARE @ModuleEndTime DATETIME = GETDATE();
    DECLARE @ModuleExecutionTime INT = DATEDIFF(MILLISECOND, @ModuleStartTime, @ModuleEndTime);

    -- Log module completion
    INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, ColumnName, RowsAffected, AnonymizationType, ExecutionTimeMs, BatchProcessed, Notes)
    VALUES ('Verification and Reporting', 'MODULE_COMPLETE', 'ALL', @TotalRowsProcessed, 'Module Execution', 
            @ModuleExecutionTime, 0, 
            'Verification and reporting completed. Status: ' + @OverallStatus + ', Risk: ' + @RiskLevel);

    PRINT '';
    PRINT '=======================================================================';
    PRINT 'VERIFICATION AND REPORTING MODULE COMPLETED';
    PRINT '=======================================================================';
    PRINT 'Total execution time: ' + CAST(DATEDIFF(SECOND, @ModuleStartTime, @ModuleEndTime) AS VARCHAR) + ' seconds';
    PRINT 'Records analyzed: ' + CAST(@TotalRowsProcessed AS VARCHAR);
    PRINT 'Operations completed: Log analysis, snapshot comparison, pattern verification, compliance reporting';
    PRINT 'Final Status: ' + @OverallStatus;
    PRINT 'Risk Level: ' + @RiskLevel;
    PRINT '';
    PRINT 'RECOMMENDATIONS:';
    IF @HighSeverityIssues > 0
    BEGIN
        PRINT '  1. ✗ DO NOT USE this database in non-production environments';
        PRINT '  2. Address high-severity issues immediately';
        PRINT '  3. Re-run specific anonymization modules';
        PRINT '  4. Verify anonymization patterns are working correctly';
    END
    ELSE IF @MediumSeverityIssues > 0
    BEGIN
        PRINT '  1. ⚠ Review medium-severity issues before use';
        PRINT '  2. Consider additional anonymization passes';
        PRINT '  3. Validate specific data patterns manually';
    END
    ELSE
    BEGIN
        PRINT '  1. ✓ Database appears fully anonymized';
        PRINT '  2. Safe for non-production use';
        PRINT '  3. Continue with performance optimization (Module 11)';
    END
    PRINT '=======================================================================';

    IF @DryRun = 1 OR @CommitChanges = 0 
        ROLLBACK TRAN 
    ELSE 
        COMMIT TRAN;

END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0 ROLLBACK TRAN;
    
    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();
    
    PRINT 'ERROR in Verification and Reporting: ' + @ErrorMessage;
    
    -- Log the error if possible
    IF OBJECT_ID('dbo.AnonymizationLog') IS NOT NULL
    BEGIN
        INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, ColumnName, RowsAffected, AnonymizationType, Notes)
        VALUES ('Verification and Reporting', 'ERROR', 'MODULE_FAILURE', 0, 'Error', @ErrorMessage);
    END
    
    RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
END CATCH