-- =====================================================================================
-- COMPLETEVIEW DATABASE ANONYMIZATION - STANDALONE MODULE
-- File: 07_URL_and_FilePath_Anonymization.sql

-- =====================================================================================
-- This is a standalone, pre-configured SQL module that can be executed directly
-- in SQL Server Management Studio without requiring PowerShell configuration.
-- 
-- IMPORTANT: 
-- 1. BACKUP YOUR DATABASE BEFORE RUNNING THIS SCRIPT
-- 2. Review the configuration values below before execution
-- 3. Execute modules in numerical order (01, 02, 03, etc.)
-- =====================================================================================

SET NOCOUNT ON;
SET XACT_ABORT ON;

-- =====================================================================================
-- CENTRALIZED CONFIGURATION LOADER
-- =====================================================================================

-- Verify configuration prerequisites
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationConfig')
BEGIN
    THROW 50001, 'AnonymizationConfig table not found. Please run Module 01 (Setup and Validation) first.', 1;
END

IF OBJECT_ID('dbo.fn_GetConfigValue') IS NULL
BEGIN
    THROW 50002, 'Configuration helper function fn_GetConfigValue not found. Please run Module 01 (Setup and Validation) first.', 1;
END

-- Load configuration values from centralized table
DECLARE @ExecutionID VARCHAR(50) = 'MODULE07_' + REPLACE(CAST(NEWID() AS VARCHAR(36)), '-', '');
DECLARE @ModuleName VARCHAR(100) = 'URL and FilePath Anonymization';

PRINT 'Starting ' + @ModuleName + '...';
PRINT 'Execution ID: ' + @ExecutionID;

DECLARE @AnonymizationSeed INT = CAST(dbo.fn_GetConfigValue('AnonymizationSeed') AS INT);
DECLARE @AnonymizedDomain NVARCHAR(100) = dbo.fn_GetConfigValue('AnonymizedDomain');
DECLARE @EmailSuffix NVARCHAR(100) = dbo.fn_GetConfigValue('EmailSuffix');
DECLARE @DefaultPassword NVARCHAR(100) = dbo.fn_GetConfigValue('DefaultPassword');
DECLARE @BatchSize INT = CAST(dbo.fn_GetConfigValue('BatchSize') AS INT);
DECLARE @DryRun BIT = CAST(dbo.fn_GetConfigValue('DryRun') AS BIT);
DECLARE @CommitChanges BIT = CAST(dbo.fn_GetConfigValue('CommitChanges') AS BIT);
DECLARE @UsePerformanceOptimizations BIT = CAST(dbo.fn_GetConfigValue('UsePerformanceOptimizations') AS BIT);
DECLARE @RequireRecentBackup BIT = CAST(dbo.fn_GetConfigValue('RequireRecentBackup') AS BIT);
DECLARE @BackupMaxAgeDays INT = CAST(dbo.fn_GetConfigValue('BackupMaxAgeDays') AS INT);

-- Path and URL configuration
DECLARE @CVE_FilePath_Prefix NVARCHAR(100) = dbo.fn_GetConfigValue('CVE_FilePath_Prefix');
DECLARE @CVE_UNCPath_Prefix NVARCHAR(100) = dbo.fn_GetConfigValue('CVE_UNCPath_Prefix');
DECLARE @CVE_StreamProfile_Prefix NVARCHAR(50) = dbo.fn_GetConfigValue('CVE_StreamProfile_Prefix');

-- Module-specific configuration
DECLARE @URLPrefix NVARCHAR(50) = ISNULL(dbo.fn_GetConfigValue('URLPrefix'), 'https://example.com');
DECLARE @FilePathPrefix NVARCHAR(100) = ISNULL(@CVE_FilePath_Prefix, 'C:\AnonymizedData');
DECLARE @UNCPathPrefix NVARCHAR(100) = ISNULL(@CVE_UNCPath_Prefix, '\\anonymized-server\share');
DECLARE @StreamProfilePrefix NVARCHAR(50) = ISNULL(@CVE_StreamProfile_Prefix, 'Profile');

-- Initialize module variables
DECLARE @TotalRowsProcessed INT = 0;
DECLARE @ModuleStartTime DATETIME = GETDATE();
DECLARE @OperationStart DATETIME;
DECLARE @RowsAffected INT;

PRINT 'Configuration loaded from centralized table for URL and FilePath Anonymization';

-- =====================================================================================
-- MODULE 07: URL AND FILEPATH ANONYMIZATION
-- =====================================================================================
-- Description: Anonymizes file paths, URLs, UNC paths, stream profiles, and volume settings
-- Security Level: HIGH - Network topology and file system structure exposure
-- =====================================================================================

PRINT '=======================================================================';
PRINT 'MODULE 07: URL AND FILEPATH ANONYMIZATION';
PRINT '=======================================================================';
PRINT 'Execution Mode: ' + CASE WHEN @DryRun = 1 THEN 'DRY RUN (Preview Only)' ELSE 'LIVE EXECUTION' END;
PRINT '';

BEGIN TRY
    BEGIN TRAN;

    -- Create pre-anonymization snapshot
    PRINT 'Creating pre-anonymization snapshot...';
    IF OBJECT_ID('dbo.sp_CreatePreAnonymizationSnapshot') IS NOT NULL
    BEGIN
        EXEC dbo.sp_CreatePreAnonymizationSnapshot 
            @ExecutionID = @ExecutionID,
            @ModuleName = @ModuleName;
    END

    -- =====================================================================================
    -- VOLUME TABLE ANONYMIZATION
    -- =====================================================================================
    PRINT 'Processing Volume table (file paths and UNC paths)...';

    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'Volume')
    BEGIN
        DECLARE @VolumeRowCount INT = (SELECT COUNT(*) FROM dbo.Volume);
        SET @OperationStart = GETDATE();
        
        IF @VolumeRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@VolumeRowCount AS VARCHAR) + ' volume records to process.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: Volume paths anonymization preview:';
                SELECT TOP 10
                    VolumeID,
                    PathAddress,
                    CASE 
                        WHEN PathAddress LIKE '\\%' THEN @UNCPathPrefix + '\volume' + CAST(VolumeID AS VARCHAR)
                        WHEN PathAddress LIKE '%:%' THEN @FilePathPrefix + '\Volume' + CAST(VolumeID AS VARCHAR)
                        ELSE @FilePathPrefix + '\Volume' + CAST(VolumeID AS VARCHAR)
                    END AS New_PathAddress,
                    VolumeUncPath,
                    @UNCPathPrefix + '\volume' + CAST(VolumeID AS VARCHAR) AS New_VolumeUncPath
                FROM dbo.Volume
                WHERE PathAddress IS NOT NULL OR VolumeUncPath IS NOT NULL;
            END
            ELSE
            BEGIN
                -- Anonymize PathAddress
                UPDATE dbo.Volume 
                SET PathAddress = CASE 
                    WHEN PathAddress LIKE '\\%' THEN @UNCPathPrefix + '\volume' + CAST(VolumeID AS VARCHAR)
                    WHEN PathAddress LIKE '%:%' THEN @FilePathPrefix + '\Volume' + CAST(VolumeID AS VARCHAR)
                    ELSE @FilePathPrefix + '\Volume' + CAST(VolumeID AS VARCHAR)
                END
                WHERE PathAddress IS NOT NULL;
                
                DECLARE @PathUpdateCount INT = @@ROWCOUNT;
                
                -- Anonymize VolumeUncPath
                UPDATE dbo.Volume 
                SET VolumeUncPath = @UNCPathPrefix + '\volume' + CAST(VolumeID AS VARCHAR)
                WHERE VolumeUncPath IS NOT NULL;
                
                DECLARE @UNCUpdateCount INT = @@ROWCOUNT;
                
                SET @RowsAffected = @PathUpdateCount + @UNCUpdateCount;
                
                -- Buffer operations for aggregated logging
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES 
                    ('URL and FilePath', 'Volume', 'PathAddress', @PathUpdateCount, 'Path Anonymization', @OperationStart, GETDATE(),
                     'Volume path addresses anonymized with sequential naming'),
                    ('URL and FilePath', 'Volume', 'VolumeUncPath', @UNCUpdateCount, 'UNC Path Anonymization', @OperationStart, GETDATE(),
                     'Volume UNC paths anonymized with sequential naming');
                
                PRINT 'Updated ' + CAST(@PathUpdateCount AS VARCHAR) + ' path addresses';
                PRINT 'Updated ' + CAST(@UNCUpdateCount AS VARCHAR) + ' UNC paths';
            END
            
            SET @TotalRowsProcessed = @TotalRowsProcessed + @RowsAffected;
        END
        ELSE
        BEGIN
            PRINT 'No volume records found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'Volume table not found in database.';
    END

    -- =====================================================================================
    -- IPSETTINGS TABLE ANONYMIZATION
    -- =====================================================================================
    PRINT '';
    PRINT 'Processing IpSettings table (IP paths and stream profiles)...';

    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'IpSettings')
    BEGIN
        DECLARE @IpSettingsRowCount INT = (SELECT COUNT(*) FROM dbo.IpSettings);
        SET @OperationStart = GETDATE();
        
        IF @IpSettingsRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@IpSettingsRowCount AS VARCHAR) + ' IP settings records to process.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: IP settings anonymization preview:';
                SELECT TOP 10
                    SettingID,
                    IpPath,
                    @FilePathPrefix + '\ip_config\setting' + CAST(SettingID AS VARCHAR) AS New_IpPath,
                    AxisStreamProfile,
                    @StreamProfilePrefix + '_' + CAST(SettingID AS VARCHAR) AS New_AxisStreamProfile
                FROM dbo.IpSettings
                WHERE IpPath IS NOT NULL OR AxisStreamProfile IS NOT NULL;
            END
            ELSE
            BEGIN
                -- Anonymize IpPath
                UPDATE dbo.IpSettings 
                SET IpPath = @FilePathPrefix + '\ip_config\setting' + CAST(SettingID AS VARCHAR)
                WHERE IpPath IS NOT NULL;
                
                DECLARE @IpPathUpdateCount INT = @@ROWCOUNT;
                
                -- Anonymize AxisStreamProfile
                UPDATE dbo.IpSettings 
                SET AxisStreamProfile = @StreamProfilePrefix + '_' + CAST(SettingID AS VARCHAR)
                WHERE AxisStreamProfile IS NOT NULL;
                
                DECLARE @StreamProfileUpdateCount INT = @@ROWCOUNT;
                
                SET @RowsAffected = @IpPathUpdateCount + @StreamProfileUpdateCount;
                
                -- Buffer operations for aggregated logging
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES 
                    ('URL and FilePath', 'IpSettings', 'IpPath', @IpPathUpdateCount, 'Path Anonymization', @OperationStart, GETDATE(),
                     'IP configuration paths anonymized'),
                    ('URL and FilePath', 'IpSettings', 'AxisStreamProfile', @StreamProfileUpdateCount, 'Profile Anonymization', @OperationStart, GETDATE(),
                     'Stream profiles anonymized with sequential naming');
                
                PRINT 'Updated ' + CAST(@IpPathUpdateCount AS VARCHAR) + ' IP paths';
                PRINT 'Updated ' + CAST(@StreamProfileUpdateCount AS VARCHAR) + ' stream profiles';
            END
            
            SET @TotalRowsProcessed = @TotalRowsProcessed + @RowsAffected;
        END
        ELSE
        BEGIN
            PRINT 'No IP settings records found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'IpSettings table not found - skipping IP settings anonymization';
    END

    -- =====================================================================================
    -- VIDEODEVICES TABLE ADDITIONAL PATH CHECKS
    -- =====================================================================================
    PRINT '';
    PRINT 'Processing VideoDevices table for additional file paths...';

    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'VideoDevices')
    BEGIN
        -- Check for path-related columns in VideoDevices
        DECLARE @VideoDevicePathColumns TABLE (ColumnName NVARCHAR(128));
        
        INSERT INTO @VideoDevicePathColumns (ColumnName)
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'VideoDevices'
        AND (COLUMN_NAME LIKE '%Path%' OR COLUMN_NAME LIKE '%URL%' OR COLUMN_NAME LIKE '%URI%' OR COLUMN_NAME LIKE '%Directory%');
        
        IF EXISTS (SELECT 1 FROM @VideoDevicePathColumns)
        BEGIN
            DECLARE @VideoDeviceRowCount INT = (SELECT COUNT(*) FROM dbo.VideoDevices);
            SET @OperationStart = GETDATE();
            
            IF @VideoDeviceRowCount > 0
            BEGIN
                PRINT 'Found ' + CAST(@VideoDeviceRowCount AS VARCHAR) + ' video device records with path columns to process.';
                
                IF @DryRun = 1
                BEGIN
                    PRINT 'DRY RUN: VideoDevices path anonymization preview:';
                    PRINT 'Path-related columns found in VideoDevices table:';
                    SELECT ColumnName FROM @VideoDevicePathColumns;
                END
                ELSE
                BEGIN
                    -- Dynamic anonymization based on found columns
                    DECLARE @PathColumnName NVARCHAR(128);
                    DECLARE @UpdateSQL NVARCHAR(MAX);
                    DECLARE @VideoDeviceUpdateCount INT = 0;
                    
                    DECLARE path_cursor CURSOR FOR
                    SELECT ColumnName FROM @VideoDevicePathColumns;
                    
                    OPEN path_cursor;
                    FETCH NEXT FROM path_cursor INTO @PathColumnName;
                    
                    WHILE @@FETCH_STATUS = 0
                    BEGIN
                        SET @UpdateSQL = 'UPDATE dbo.VideoDevices SET ' + QUOTENAME(@PathColumnName) +
                                        ' = ''' + @FilePathPrefix + '\video_device'' + CAST(DeviceID AS VARCHAR(10)) WHERE ' +
                                        QUOTENAME(@PathColumnName) + ' IS NOT NULL';
                        
                        EXEC sp_executesql @UpdateSQL;
                        SET @VideoDeviceUpdateCount = @VideoDeviceUpdateCount + @@ROWCOUNT;
                        
                        PRINT 'Updated ' + @PathColumnName + ' column';
                        
                        FETCH NEXT FROM path_cursor INTO @PathColumnName;
                    END
                    
                    CLOSE path_cursor;
                    DEALLOCATE path_cursor;
                    
                    -- Buffer operation for aggregated logging
                    INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                    VALUES ('URL and FilePath', 'VideoDevices', 'PathColumns', @VideoDeviceUpdateCount, 'Path Anonymization', @OperationStart, GETDATE(),
                            'Video device path columns anonymized');
                    
                    PRINT 'Updated ' + CAST(@VideoDeviceUpdateCount AS VARCHAR) + ' video device path records';
                    SET @TotalRowsProcessed = @TotalRowsProcessed + @VideoDeviceUpdateCount;
                END
            END
            ELSE
            BEGIN
                PRINT 'No video device records found to anonymize.';
            END
        END
        ELSE
        BEGIN
            PRINT 'No path-related columns found in VideoDevices table.';
        END
    END
    ELSE
    BEGIN
        PRINT 'VideoDevices table not found - skipping video device path anonymization';
    END

    -- =====================================================================================
    -- CONFIGURATION TABLES URL ANONYMIZATION
    -- =====================================================================================
    PRINT '';
    PRINT 'Processing configuration tables for URLs and web addresses...';

    -- Anonymize any configuration tables with URL patterns
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'Configuration')
    BEGIN
        DECLARE @ConfigRowCount INT = (
            SELECT COUNT(*) 
            FROM dbo.Configuration 
            WHERE ConfigValue LIKE 'http%' OR ConfigValue LIKE 'ftp%' OR ConfigValue LIKE '\\%'
        );
        
        SET @OperationStart = GETDATE();
        
        IF @ConfigRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@ConfigRowCount AS VARCHAR) + ' configuration records with URLs/paths to process.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: Configuration URLs/paths anonymization preview:';
                SELECT TOP 10
                    ConfigKey,
                    ConfigValue,
                    CASE 
                        WHEN ConfigValue LIKE 'http%' THEN @URLPrefix + '/config/' + CAST(ConfigID AS VARCHAR)
                        WHEN ConfigValue LIKE 'ftp%' THEN 'ftp://ftp.example.com/config/' + CAST(ConfigID AS VARCHAR)
                        WHEN ConfigValue LIKE '\\%' THEN @UNCPathPrefix + '\config\' + CAST(ConfigID AS VARCHAR)
                        ELSE ConfigValue
                    END AS New_ConfigValue
                FROM dbo.Configuration
                WHERE ConfigValue LIKE 'http%' OR ConfigValue LIKE 'ftp%' OR ConfigValue LIKE '\\%';
            END
            ELSE
            BEGIN
                -- Anonymize URLs and paths in configuration
                UPDATE dbo.Configuration 
                SET ConfigValue = CASE 
                    WHEN ConfigValue LIKE 'http%' THEN @URLPrefix + '/config/' + CAST(ConfigID AS VARCHAR)
                    WHEN ConfigValue LIKE 'ftp%' THEN 'ftp://ftp.example.com/config/' + CAST(ConfigID AS VARCHAR)
                    WHEN ConfigValue LIKE '\\%' THEN @UNCPathPrefix + '\config\' + CAST(ConfigID AS VARCHAR)
                    ELSE ConfigValue
                END
                WHERE ConfigValue LIKE 'http%' OR ConfigValue LIKE 'ftp%' OR ConfigValue LIKE '\\%';
                
                DECLARE @ConfigUpdateCount INT = @@ROWCOUNT;
                
                -- Buffer operation for aggregated logging
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('URL and FilePath', 'Configuration', 'ConfigValue', @ConfigUpdateCount, 'URL/Path Anonymization', @OperationStart, GETDATE(),
                        'Configuration URLs and paths anonymized');
                
                PRINT 'Updated ' + CAST(@ConfigUpdateCount AS VARCHAR) + ' configuration URLs/paths';
                SET @TotalRowsProcessed = @TotalRowsProcessed + @ConfigUpdateCount;
            END
        END
        ELSE
        BEGIN
            PRINT 'No configuration records with URLs/paths found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'Configuration table not found - skipping configuration URL anonymization';
    END

    -- =====================================================================================
    -- CORS AND SECURITY POLICY ANONYMIZATION
    -- =====================================================================================
    PRINT '';
    PRINT 'Processing CORS and security policy configurations...';

    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'SecurityPolicies')
    BEGIN
        DECLARE @SecurityPolicyRowCount INT = (
            SELECT COUNT(*) 
            FROM dbo.SecurityPolicies 
            WHERE PolicyValue LIKE 'http%' OR PolicyValue LIKE '*://%'
        );
        
        SET @OperationStart = GETDATE();
        
        IF @SecurityPolicyRowCount > 0
        BEGIN
            PRINT 'Found ' + CAST(@SecurityPolicyRowCount AS VARCHAR) + ' security policy records with URLs to process.';
            
            IF @DryRun = 1
            BEGIN
                PRINT 'DRY RUN: Security policies URL anonymization preview:';
                SELECT TOP 10
                    PolicyID,
                    PolicyName,
                    PolicyValue,
                    @URLPrefix + '/allowed-origin' AS New_PolicyValue
                FROM dbo.SecurityPolicies
                WHERE PolicyValue LIKE 'http%' OR PolicyValue LIKE '*://%';
            END
            ELSE
            BEGIN
                -- Anonymize CORS and security policy URLs
                UPDATE dbo.SecurityPolicies 
                SET PolicyValue = @URLPrefix + '/allowed-origin'
                WHERE PolicyValue LIKE 'http%' OR PolicyValue LIKE '*://%';
                
                DECLARE @SecurityPolicyUpdateCount INT = @@ROWCOUNT;
                
                -- Buffer operation for aggregated logging
                INSERT INTO dbo.AnonymizationLogBuffer (ModuleName, TableName, ColumnName, RowsAffected, OperationType, StartTime, EndTime, Notes)
                VALUES ('URL and FilePath', 'SecurityPolicies', 'PolicyValue', @SecurityPolicyUpdateCount, 'URL Anonymization', @OperationStart, GETDATE(),
                        'Security policy URLs anonymized for CORS compliance');
                
                PRINT 'Updated ' + CAST(@SecurityPolicyUpdateCount AS VARCHAR) + ' security policy URLs';
                SET @TotalRowsProcessed = @TotalRowsProcessed + @SecurityPolicyUpdateCount;
            END
        END
        ELSE
        BEGIN
            PRINT 'No security policy records with URLs found to anonymize.';
        END
    END
    ELSE
    BEGIN
        PRINT 'SecurityPolicies table not found - skipping security policy URL anonymization';
    END

    -- =====================================================================================
    -- MODULE COMPLETION
    -- =====================================================================================
    DECLARE @ModuleEndTime DATETIME = GETDATE();
    DECLARE @ModuleExecutionTime INT = DATEDIFF(MILLISECOND, @ModuleStartTime, @ModuleEndTime);

    -- Flush aggregated logging buffer to reduce transaction log bloat
    IF OBJECT_ID('dbo.sp_FlushAnonymizationBuffer') IS NOT NULL
    BEGIN
        EXEC dbo.sp_FlushAnonymizationBuffer @DryRun = @DryRun;
    END

    -- Log module completion
    INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, ColumnName, RowsAffected, AnonymizationType, ExecutionTimeMs, BatchProcessed, Notes)
    VALUES ('URL and FilePath', 'MODULE_COMPLETE', 'ALL', @TotalRowsProcessed, 'Module Execution', 
            @ModuleExecutionTime, 0, 
            'URL and file path anonymization completed in ' + CAST(DATEDIFF(SECOND, @ModuleStartTime, @ModuleEndTime) AS VARCHAR) + ' seconds');

    PRINT '';
    PRINT '=======================================================================';
    PRINT 'URL AND FILEPATH ANONYMIZATION MODULE COMPLETED';
    PRINT '=======================================================================';
    PRINT 'Total execution time: ' + CAST(DATEDIFF(SECOND, @ModuleStartTime, @ModuleEndTime) AS VARCHAR) + ' seconds';
    PRINT 'Total rows processed: ' + CAST(@TotalRowsProcessed AS VARCHAR);
    PRINT 'Operations completed: Volume paths, UNC paths, IP settings, stream profiles, configuration URLs, security policies';
    PRINT 'Dry run mode: ' + CASE WHEN @DryRun = 1 THEN 'YES (No data modified)' ELSE 'NO (Data modified)' END;
    PRINT '';
    PRINT 'SECURITY RECOMMENDATIONS:';
    PRINT '1. Verify all file paths point to anonymized locations';
    PRINT '2. Check UNC paths don''t expose internal network structure';
    PRINT '3. Validate stream profiles are properly anonymized';
    PRINT '4. Review configuration URLs for external dependencies';
    PRINT '5. Ensure CORS policies don''t expose sensitive origins';
    PRINT '6. Verify backup and archive paths are anonymized';
    PRINT '7. Check CORS whitelist entries for security policy compliance';
    PRINT '8. Validate file paths don''t expose sensitive directory structures';
    PRINT '9. Consider anonymizing any custom application-specific paths';
    PRINT '10. Verify stream profile configurations are properly anonymized';
    PRINT '=======================================================================';

    -- Create post-anonymization snapshot
    IF @DryRun = 0 AND OBJECT_ID('dbo.sp_CreatePostAnonymizationSnapshot') IS NOT NULL
    BEGIN
        PRINT 'Creating post-anonymization snapshot...';
        EXEC dbo.sp_CreatePostAnonymizationSnapshot 
            @ExecutionID = @ExecutionID,
            @ModuleName = @ModuleName;
    END

    IF @DryRun = 1 OR @CommitChanges = 0 
        ROLLBACK TRAN 
    ELSE 
        COMMIT TRAN;

END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0 ROLLBACK TRAN;
    
    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();
    
    PRINT 'ERROR in URL and FilePath Anonymization: ' + @ErrorMessage;
    
    -- Log the error if possible
    IF OBJECT_ID('dbo.AnonymizationLog') IS NOT NULL
    BEGIN
        INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, ColumnName, RowsAffected, AnonymizationType, Notes)
        VALUES ('URL and FilePath', 'ERROR', 'MODULE_FAILURE', 0, 'Error', @ErrorMessage);
    END
    
    RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
END CATCH