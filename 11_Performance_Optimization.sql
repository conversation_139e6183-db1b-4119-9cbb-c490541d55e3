-- Create temporary tables for analysis
CREATE TABLE #IndexFragmentation (
    DatabaseName NVARCHAR(128),
    SchemaName NVARCHAR(128),
    TableName NVARCHAR(128),
    IndexName NVARCHAR(128),
    IndexType NVARCHAR(60),
    FragmentationPercent DECIMAL(5,2),
    PageCount BIGINT,
    SizeInMB DECIMAL(10,2),
    NeedsRebuild BIT
);

CREATE TABLE #StatisticsAnalysis (
    SchemaName NVARCHAR(128),
    TableName NVARCHAR(128),
    StatisticsName NVARCHAR(128),
    LastUpdated DATETIME,
    DaysOld INT,
    NeedsUpdate BIT,
    RowsSampled BIGINT,
    TotalRows BIGINT
);

PRINT 'Current log file size: ' + CAST(@LogFileSizeMB AS VARCHAR) + ' MB';
PRINT '';

-- =====================================================================================
-- INDEX FRAGMENTATION ANALYSIS
-- =====================================================================================
PRINT 'Analyzing index fragmentation...';

-- Get index fragmentation information for all user tables
INSERT INTO #IndexFragmentation
SELECT 
    DB_NAME() AS DatabaseName,
    SCHEMA_NAME(t.schema_id) AS SchemaName,
    t.name AS TableName,
    i.name AS IndexName,
    i.type_desc AS IndexType,
    ips.avg_fragmentation_in_percent AS FragmentationPercent,
    ips.page_count AS PageCount,
    (ips.page_count * 8.0) / 1024 AS SizeInMB,
    CASE WHEN ips.avg_fragmentation_in_percent > @FragmentationThreshold THEN 1 ELSE 0 END AS NeedsRebuild
FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'LIMITED') ips
INNER JOIN sys.indexes i ON ips.object_id = i.object_id AND ips.index_id = i.index_id
INNER JOIN sys.tables t ON i.object_id = t.object_id
WHERE t.type = 'U'  -- User tables only (excludes system tables)
AND ips.avg_fragmentation_in_percent > 5  -- Only show indexes with > 5% fragmentation
AND ips.page_count > 100  -- Only include indexes with significant size
AND i.index_id > 0  -- Exclude heaps (tables without clustered index)
ORDER BY ips.avg_fragmentation_in_percent DESC;

-- Display fragmentation analysis
DECLARE @TotalIndexesAnalyzed INT = (SELECT COUNT(*) FROM #IndexFragmentation);
DECLARE @FragmentedIndexesFound INT = (SELECT COUNT(*) FROM #IndexFragmentation WHERE NeedsRebuild = 1);
SET @FragmentedIndexCount = @FragmentedIndexesFound;

PRINT 'INDEX FRAGMENTATION ANALYSIS (' + CAST(@TotalIndexesAnalyzed AS VARCHAR) + ' indexes analyzed):';
PRINT 'Indexes needing rebuild: ' + CAST(@FragmentedIndexCount AS VARCHAR);
PRINT '';

IF @TotalIndexesAnalyzed > 0
BEGIN
    SELECT 
        SchemaName + '.' + TableName + '.' + IndexName AS [Index],
        IndexType,
        CAST(FragmentationPercent AS DECIMAL(5,1)) AS [Fragmentation %],
        CAST(SizeInMB AS DECIMAL(10,1)) AS [Size MB],
        CASE WHEN NeedsRebuild = 1 THEN 'YES' ELSE 'NO' END AS [Needs Rebuild]
    FROM #IndexFragmentation
    ORDER BY FragmentationPercent DESC;
END
ELSE
BEGIN
    PRINT 'No indexes found requiring analysis.';
END

-- =====================================================================================
-- STATISTICS ANALYSIS
-- =====================================================================================
PRINT '';
PRINT 'Analyzing table statistics...';

-- Get statistics information for all user tables that exist
INSERT INTO #StatisticsAnalysis
SELECT 
    SCHEMA_NAME(t.schema_id) AS SchemaName,
    t.name AS TableName,
    s.name AS StatisticsName,
    sp.last_updated AS LastUpdated,
    DATEDIFF(DAY, sp.last_updated, GETDATE()) AS DaysOld,
    CASE WHEN DATEDIFF(DAY, sp.last_updated, GETDATE()) > 7 THEN 1 ELSE 0 END AS NeedsUpdate,
    sp.rows_sampled AS RowsSampled,
    sp.rows AS TotalRows
FROM sys.stats s
INNER JOIN sys.tables t ON s.object_id = t.object_id
CROSS APPLY sys.dm_db_stats_properties(s.object_id, s.stats_id) sp
WHERE t.type = 'U'  -- User tables only (excludes system tables)
AND t.name NOT LIKE 'AnonymizationLog%'  -- Exclude our own logging tables
AND sp.rows > 0  -- Only tables with data
ORDER BY sp.last_updated;

DECLARE @TotalStatsAnalyzed INT = (SELECT COUNT(*) FROM #StatisticsAnalysis);
DECLARE @OutdatedStatsFound INT = (SELECT COUNT(*) FROM #StatisticsAnalysis WHERE NeedsUpdate = 1);
SET @OutdatedStatsCount = @OutdatedStatsFound;

PRINT 'STATISTICS ANALYSIS (' + CAST(@TotalStatsAnalyzed AS VARCHAR) + ' statistics analyzed):';
PRINT 'Statistics needing update: ' + CAST(@OutdatedStatsCount AS VARCHAR);

IF @TotalStatsAnalyzed > 0
BEGIN
    PRINT '';
    PRINT 'OLDEST STATISTICS (Top 10):';
    SELECT TOP 10
        SchemaName + '.' + TableName AS [Table],
        StatisticsName,
        LastUpdated,
        DaysOld AS [Days Old],
        CAST(TotalRows AS BIGINT) AS [Total Rows],
        CASE WHEN NeedsUpdate = 1 THEN 'YES' ELSE 'NO' END AS [Needs Update]
    FROM #StatisticsAnalysis
    ORDER BY LastUpdated;
END

-- Show key anonymization tables found
PRINT '';
PRINT 'KEY ANONYMIZATION TABLES FOUND:';
SELECT DISTINCT
    SchemaName + '.' + TableName AS [Table Name],
    MAX(TotalRows) AS [Row Count],
    MAX(CASE WHEN NeedsUpdate = 1 THEN 'YES' ELSE 'NO' END) AS [Needs Stats Update],
    COUNT(*) AS [Statistics Count]
FROM #StatisticsAnalysis
WHERE TableName IN ('Camera', 'Server', 'User', 'VideoDevices', 'AlarmDevices', 'AudioInputDevice', 'MotionZone', 'EmailMessage', 'ActiveDirectory', 'NetworkDevice', 'Integration', 'Credential')
    OR TableName LIKE '%Camera%' 
    OR TableName LIKE '%Server%' 
    OR TableName LIKE '%User%' 
    OR TableName LIKE '%Device%'
    OR TableName LIKE '%Email%'
    OR TableName LIKE '%Network%'
GROUP BY SchemaName, TableName
ORDER BY MAX(TotalRows) DESC;

-- =====================================================================================
-- INDEX REBUILD OPERATIONS (Fixed implementation)
-- =====================================================================================
IF @RebuildIndexes = 1 AND @DryRun = 0
BEGIN
    PRINT '';
    PRINT 'Rebuilding fragmented indexes...';
    
    DECLARE @SQL NVARCHAR(MAX);
    DECLARE @IndexFullName VARCHAR(500);
    DECLARE @FragmentationBefore DECIMAL(5,2);
    DECLARE @IndexCounter INT = 0;
    
    DECLARE index_cursor CURSOR FOR
    SELECT 
        'ALTER INDEX [' + IndexName + '] ON [' + SchemaName + '].[' + TableName + '] REBUILD;',
        SchemaName + '.' + TableName + '.' + IndexName,
        FragmentationPercent
    FROM #IndexFragmentation 
    WHERE NeedsRebuild = 1
    ORDER BY SizeInMB DESC; -- Rebuild largest indexes first
    
    OPEN index_cursor;
    FETCH NEXT FROM index_cursor INTO @SQL, @IndexFullName, @FragmentationBefore;
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        SET @IndexCounter = @IndexCounter + 1;
        PRINT 'Rebuilding index (' + CAST(@IndexCounter AS VARCHAR) + '/' + CAST(@FragmentedIndexCount AS VARCHAR) + '): ' + @IndexFullName;
        PRINT 'Fragmentation before: ' + CAST(@FragmentationBefore AS VARCHAR) + '%';
        
        BEGIN TRY
            EXEC sp_executesql @SQL;
            SET @IndexesRebuilt = @IndexesRebuilt + 1;
            
            INSERT INTO #OptimizationResults (Category, ObjectName, Metric, BeforeValue, AfterValue, Notes)
            VALUES ('Index Rebuild', @IndexFullName, 'Fragmentation %', CAST(@FragmentationBefore AS VARCHAR), 'Rebuilt', 'Index successfully rebuilt');
            
            PRINT 'SUCCESS: Index rebuilt successfully.';
        END TRY
        BEGIN CATCH
            DECLARE @IndexError NVARCHAR(4000) = ERROR_MESSAGE();
            PRINT 'ERROR rebuilding index: ' + @IndexError;
            
            INSERT INTO #OptimizationResults (Category, ObjectName, Metric, BeforeValue, AfterValue, Notes)
            VALUES ('Index Rebuild', @IndexFullName, 'Error', CAST(@FragmentationBefore AS VARCHAR), 'FAILED', 'Error: ' + @IndexError);
        END CATCH
        
        PRINT '';
        FETCH NEXT FROM index_cursor INTO @SQL, @IndexFullName, @FragmentationBefore;
    END
    
    CLOSE index_cursor;
    DEALLOCATE index_cursor;
    
    PRINT 'Index rebuild completed. Processed ' + CAST(@IndexCounter AS VARCHAR) + ' indexes.';
    PRINT 'Successfully rebuilt: ' + CAST(@IndexesRebuilt AS VARCHAR) + ' indexes.';
END
ELSE IF @RebuildIndexes = 1 AND @DryRun = 1
BEGIN
    PRINT '';
    PRINT 'DRY RUN: Index rebuild operations that would be performed:';
    SELECT 
        'ALTER INDEX [' + IndexName + '] ON [' + SchemaName + '].[' + TableName + '] REBUILD;' AS [SQL Command],
        CAST(FragmentationPercent AS DECIMAL(5,1)) AS [Current Fragmentation %],
        CAST(SizeInMB AS DECIMAL(10,1)) AS [Size MB]
    FROM #IndexFragmentation 
    WHERE NeedsRebuild = 1
    ORDER BY SizeInMB DESC;
END
ELSE
BEGIN
    PRINT '';
    PRINT 'Index rebuild skipped (disabled in configuration).';
END

-- =====================================================================================
-- STATISTICS UPDATE OPERATIONS
-- =====================================================================================
IF @UpdateStatistics = 1 AND @DryRun = 0
BEGIN
    PRINT '';
    PRINT 'Updating outdated statistics...';
    
    DECLARE @TableName NVARCHAR(200);
    DECLARE @StatsCounter INT = 0;
    
    DECLARE stats_cursor CURSOR FOR
    SELECT DISTINCT SchemaName + '.' + TableName
    FROM #StatisticsAnalysis 
    WHERE NeedsUpdate = 1
    ORDER BY SchemaName, TableName;
    
    OPEN stats_cursor;
    FETCH NEXT FROM stats_cursor INTO @TableName;
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        SET @StatsCounter = @StatsCounter + 1;
        PRINT 'Updating statistics for table: ' + @TableName;
        
        BEGIN TRY
            SET @SQL = 'UPDATE STATISTICS [' + REPLACE(@TableName, '.', '].[') + '] WITH FULLSCAN;';
            EXEC sp_executesql @SQL;
            SET @StatsUpdated = @StatsUpdated + 1;
            
            INSERT INTO #OptimizationResults (Category, ObjectName, Metric, BeforeValue, AfterValue, Notes)
            VALUES ('Statistics Update', @TableName, 'Last Updated', 'Outdated', 'Updated', 'Statistics updated with FULLSCAN');
            
            PRINT 'SUCCESS: Statistics updated for ' + @TableName;
        END TRY
        BEGIN CATCH
            DECLARE @StatsError NVARCHAR(4000) = ERROR_MESSAGE();
            PRINT 'ERROR updating statistics for ' + @TableName + ': ' + @StatsError;
            
            INSERT INTO #OptimizationResults (Category, ObjectName, Metric, BeforeValue, AfterValue, Notes)
            VALUES ('Statistics Update', @TableName, 'Error', 'Outdated', 'FAILED', 'Error: ' + @StatsError);
        END CATCH
        
        FETCH NEXT FROM stats_cursor INTO @TableName;
    END
    
    CLOSE stats_cursor;
    DEALLOCATE stats_cursor;
    
    PRINT '';
    PRINT 'Statistics update completed. Processed ' + CAST(@StatsCounter AS VARCHAR) + ' tables.';
    PRINT 'Successfully updated: ' + CAST(@StatsUpdated AS VARCHAR) + ' tables.';
END
ELSE IF @UpdateStatistics = 1 AND @DryRun = 1
BEGIN
    PRINT '';
    PRINT 'DRY RUN: Statistics update operations that would be performed:';
    SELECT DISTINCT
        'UPDATE STATISTICS [' + REPLACE(SchemaName + '.' + TableName, '.', '].[') + '] WITH FULLSCAN;' AS [SQL Command],
        SchemaName + '.' + TableName AS [Table Name],
        COUNT(*) AS [Statistics Count],
        MIN(LastUpdated) AS [Oldest Statistic],
        MAX(DaysOld) AS [Max Days Old]
    FROM #StatisticsAnalysis 
    WHERE NeedsUpdate = 1
    GROUP BY SchemaName, TableName
    ORDER BY MAX(DaysOld) DESC;
END
ELSE
BEGIN
    PRINT '';
    PRINT 'Statistics update skipped (disabled in configuration).';
END

-- =====================================================================================
-- POST-OPTIMIZATION ANALYSIS (Enhanced implementation)
-- =====================================================================================
PRINT '';
PRINT 'Post-optimization analysis...';

-- Update file sizes after optimization
DECLARE @FinalDataFileSizeMB DECIMAL(15,2), @FinalLogFileSizeMB DECIMAL(15,2);
SELECT 
    @FinalDataFileSizeMB = SUM(CASE WHEN type = 0 THEN size * 8.0 / 1024 END),
    @FinalLogFileSizeMB = SUM(CASE WHEN type = 1 THEN size * 8.0 / 1024 END)
FROM sys.database_files;

-- Calculate size changes
DECLARE @DataSizeChange DECIMAL(15,2) = @FinalDataFileSizeMB - @DataFileSizeMB;
DECLARE @LogSizeChange DECIMAL(15,2) = @FinalLogFileSizeMB - @LogFileSizeMB;

-- Update optimization results with final sizes
UPDATE #OptimizationResults 
SET AfterValue = CAST(@FinalDataFileSizeMB AS VARCHAR),
    ImprovementPct = CASE WHEN @DataFileSizeMB > 0 THEN ((@DataFileSizeMB - @FinalDataFileSizeMB) / @DataFileSizeMB) * 100 ELSE 0 END
WHERE Category = 'Database Size' AND Metric = 'Data File Size (MB)';

UPDATE #OptimizationResults 
SET AfterValue = CAST(@FinalLogFileSizeMB AS VARCHAR),
    ImprovementPct = CASE WHEN @LogFileSizeMB > 0 THEN ((@LogFileSizeMB - @FinalLogFileSizeMB) / @LogFileSizeMB) * 100 ELSE 0 END
WHERE Category = 'Database Size' AND Metric = 'Log File Size (MB)';

PRINT 'Final data file size: ' + CAST(@FinalDataFileSizeMB AS VARCHAR) + ' MB (Change: ' + 
      CASE WHEN @DataSizeChange >= 0 THEN '+' ELSE '' END + CAST(@DataSizeChange AS VARCHAR) + ' MB)';
PRINT 'Final log file size: ' + CAST(@FinalLogFileSizeMB AS VARCHAR) + ' MB (Change: ' + 
      CASE WHEN @LogSizeChange >= 0 THEN '+' ELSE '' END + CAST(@LogSizeChange AS VARCHAR) + ' MB)';

-- Add summary statistics to results
INSERT INTO #OptimizationResults (Category, ObjectName, Metric, BeforeValue, AfterValue, Notes)
VALUES 
    ('Summary', 'Performance Optimization', 'Indexes Rebuilt', CAST(@FragmentedIndexCount AS VARCHAR), CAST(@IndexesRebuilt AS VARCHAR), 'Total indexes successfully rebuilt'),
    ('Summary', 'Performance Optimization', 'Statistics Updated', CAST(@OutdatedStatsCount AS VARCHAR), CAST(@StatsUpdated AS VARCHAR), 'Total table statistics updated'),
    ('Summary', 'Performance Optimization', 'Execution Time', '0', CAST(DATEDIFF(SECOND, @StartTime, GETDATE()) AS VARCHAR) + ' seconds', 'Total optimization time');

-- =====================================================================================
-- OPTIMIZATION RESULTS SUMMARY
-- =====================================================================================
PRINT '';
PRINT 'OPTIMIZATION RESULTS SUMMARY:';
PRINT '============================';

SELECT 
    Category,
    ObjectName,
    Metric,
    BeforeValue,
    ISNULL(AfterValue, 'N/A') AS AfterValue,
    CASE 
        WHEN ImprovementPct IS NOT NULL AND ImprovementPct != 0 THEN CAST(ROUND(ImprovementPct, 2) AS VARCHAR) + '%'
        ELSE 'N/A'
    END AS Improvement,
    Notes
FROM #OptimizationResults
ORDER BY 
    CASE Category 
        WHEN 'Summary' THEN 1
        WHEN 'Database Size' THEN 2
        WHEN 'Index Rebuild' THEN 3
        WHEN 'Statistics Update' THEN 4
        ELSE 5
    END,
    ObjectName;

-- =====================================================================================
-- MODULE COMPLETION AND STATUS
-- =====================================================================================
DECLARE @ModuleEndTime DATETIME = GETDATE();
DECLARE @TotalExecutionTime INT = DATEDIFF(SECOND, @StartTime, @ModuleEndTime);

PRINT '';
PRINT '=======================================================================';
PRINT 'MODULE 11: PERFORMANCE OPTIMIZATION COMPLETED';
PRINT '=======================================================================';
PRINT 'Total execution time: ' + CAST(@TotalExecutionTime AS VARCHAR) + ' seconds';
PRINT 'Indexes analyzed: ' + CAST(@TotalIndexesAnalyzed AS VARCHAR);
PRINT 'Indexes rebuilt: ' + CAST(@IndexesRebuilt AS VARCHAR) + '/' + CAST(@FragmentedIndexCount AS VARCHAR);
PRINT 'Statistics analyzed: ' + CAST(@TotalStatsAnalyzed AS VARCHAR);
PRINT 'Statistics updated: ' + CAST(@StatsUpdated AS VARCHAR) + '/' + CAST(@OutdatedStatsCount AS VARCHAR);
PRINT 'Data file size change: ' + CASE WHEN @DataSizeChange >= 0 THEN '+' ELSE '' END + CAST(@DataSizeChange AS VARCHAR) + ' MB';
PRINT 'Log file size change: ' + CASE WHEN @LogSizeChange >= 0 THEN '+' ELSE '' END + CAST(@LogSizeChange AS VARCHAR) + ' MB';

IF @IndexesRebuilt > 0 OR @StatsUpdated > 0
BEGIN
    PRINT 'Status: OPTIMIZATION SUCCESSFUL';
END
ELSE IF @FragmentedIndexCount = 0 AND @OutdatedStatsCount = 0
BEGIN
    PRINT 'Status: NO OPTIMIZATION NEEDED - Database already optimized';
END
ELSE
BEGIN
    PRINT 'Status: OPTIMIZATION SKIPPED - Check configuration settings';
END

-- =====================================================================================
-- PERFORMANCE RECOMMENDATIONS (Enhanced implementation)
-- =====================================================================================
PRINT '';
PRINT 'PERFORMANCE RECOMMENDATIONS:';
PRINT '=======================================================================';

-- Analyze current state and provide recommendations
IF @FragmentedIndexCount > 10
    PRINT '- Consider enabling automatic index maintenance for this database';

IF @OutdatedStatsCount > 5
    PRINT '- Consider scheduling regular statistics updates (weekly or bi-weekly)';

IF @FinalLogFileSizeMB > (@FinalDataFileSizeMB * 2)
    PRINT '- Transaction log is large. Consider implementing regular log backups (do NOT shrink files)';

IF @FinalDataFileSizeMB > 10000
    PRINT '- Large database detected. Consider implementing maintenance plans for regular optimization';

-- Database-specific recommendations based on size and complexity
IF @TotalIndexesAnalyzed > 100
    PRINT '- Complex database with many indexes. Consider index usage analysis';

IF EXISTS (SELECT 1 FROM #IndexFragmentation WHERE SizeInMB > 1000)
    PRINT '- Very large indexes detected. Consider partitioning for better performance';

-- Always provide general recommendations
PRINT '- Monitor index fragmentation regularly (monthly)';
PRINT '- Update statistics after large data modifications';
PRINT '- Consider implementing automated maintenance plans';
PRINT '- Monitor database growth and plan capacity accordingly';
PRINT '- NEVER use DBCC SHRINKFILE/SHRINKDATABASE - it causes harmful fragmentation';

-- Specific recommendations based on anonymization context
PRINT '';
PRINT 'ANONYMIZATION-SPECIFIC RECOMMENDATIONS:';
PRINT '- Schedule optimization after each anonymization cycle';
PRINT '- Monitor performance impact of anonymization operations';
PRINT '- Consider index maintenance during anonymization process';

-- Log optimization completion
IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationLog')
BEGIN
    INSERT INTO dbo.AnonymizationLog (
        TableName, ColumnName, RowsAffected, AnonymizationType, 
        ExecutionTimeMs, BatchProcessed, Notes, Operation, LogDate, IsPreview
    )
    VALUES (
        'PerformanceOptimization', 'DatabaseMaintenance', 
        @IndexesRebuilt + @StatsUpdated, 'Performance Optimization',
        DATEDIFF(MILLISECOND, @StartTime, GETDATE()), 0, 
        'Optimized ' + CAST(@IndexesRebuilt AS VARCHAR) + ' indexes and ' + CAST(@StatsUpdated AS VARCHAR) + ' statistics',
        'OPTIMIZATION_COMPLETED', GETDATE(), CAST(@DryRun AS BIT)
    );
END

END TRY
BEGIN CATCH
    -- Log error
    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
    PRINT 'ERROR during performance optimization: ' + @ErrorMessage;
    
    -- Log error to AnonymizationLog if possible
    IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationLog')
    BEGIN
        INSERT INTO dbo.AnonymizationLog (
            TableName, ColumnName, RowsAffected, AnonymizationType, 
            ExecutionTimeMs, BatchProcessed, Notes, Operation, LogDate, IsPreview
        )
        VALUES (
            'PerformanceOptimization', 'ERROR', 0, 'Performance Optimization',
            DATEDIFF(MILLISECOND, @StartTime, GETDATE()), 0, 
            'ERROR: ' + @ErrorMessage,
            'OPTIMIZATION_ERROR', GETDATE(), CAST(@DryRun AS BIT)
        );
    END
    
    -- Re-raise the error
    THROW;
END CATCH

-- =====================================================================================
-- CLEANUP TEMPORARY TABLES (Always executed regardless of success/failure)
-- =====================================================================================
-- Clean up temporary tables
IF OBJECT_ID('tempdb..#OptimizationResults') IS NOT NULL DROP TABLE #OptimizationResults;
IF OBJECT_ID('tempdb..#IndexFragmentation') IS NOT NULL DROP TABLE #IndexFragmentation;
IF OBJECT_ID('tempdb..#StatisticsAnalysis') IS NOT NULL DROP TABLE #StatisticsAnalysis;

PRINT '';
PRINT 'Performance optimization module cleanup completed.';
PRINT 'Ready for Module 12: Final Verification';
PRINT '=======================================================================';